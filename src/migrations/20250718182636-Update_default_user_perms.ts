import { Functionality, User } from '@root/shared/schemas';
import { DEFAULT_FUNCTIONALITIES } from '@root/soar/helpers/constants';
import { getUserRole } from '@root/soar/helpers/functions/user.functions';

const up = async () => {
  const users = await User.find({ deleted: false, enabled: true });
  const normalUsers = users.filter((user: any) => !user.isAdmin && !user.isManager);

  for (const user of normalUsers) {
    const role = getUserRole(user);
    if (Object.keys(DEFAULT_FUNCTIONALITIES).includes(role)) {
      const functionalities = DEFAULT_FUNCTIONALITIES[role];
      const permissions = await Functionality.getEnabledPermissions(functionalities);

      await User.findByIdAndUpdate(user._id, {
        $addToSet: { permissions: { $each: permissions } },
      });
    }
  }
};

module.exports = {
  up,
  down: async () => {},
};
