import { RportScript } from '@root/services/Rport/schemas';
import { Logger } from '@root/shared/helpers/classes/logger.class';

const up = async () => {
  // Get all scripts where kernel property is not set
  const scriptsToUpdate = await RportScript.find({
    kernel: { $exists: false },
  });

  if (scriptsToUpdate.length === 0) {
    Logger.info('No scripts found without kernel property');
    return;
  }

  // Prepare bulk operations
  const bulkOps = scriptsToUpdate.map((script) => ({
    updateOne: {
      filter: { _id: script._id },
      update: {
        $set: {
          kernel:
            script.shellType === 'cmd' || script.shellType === 'powershell' ? 'windows' : 'linux',
        },
      },
    },
  }));

  // Execute bulk update
  const result = await RportScript.bulkWrite(bulkOps);

  Logger.info(`Updated ${result.modifiedCount} RportScript documents with kernel property`);
};

module.exports = {
  up,
  down: async () => {},
};
