import { Router } from 'express';
import {
  getSoftwareVulnerabilities,
  getSoftwareVulnerabilitiesByCVEId,
  getSoftwareVulnerabilitiesBySoftwareId,
  searchBySoftwareOrCVE,
  getSoftwareVulnerabilitiesByHostIdGrouped,
  getSoftwareVulnerabilitiesByHostId,
  getCVEsListForEachIntoSoftwareList,
  getCVEsCountForEachIntoSoftwareList,
  getCVEsByOneSoftware,
  getInformationByCVEId,
  searchDataFromAppsOrCVEs,
  filterVulnerabilitiesByAppAndCVE,
  exportVulnerabilities,
  getMetricsForPatchingCadence,
  getTopVulnerableApps,
  getMetricsForVulnerabilitiesDistribution,
  getMetricForAffectedHostsByCVE,
  getMetricsForVulnerabilitiesDistributionByHost,
  exportMetricsForVulnerabilitiesDistributionByHost,
  exportMetricForAffectedHostsByCVE,
  getCveExplanationController,
  getMetricsForVulnerabilitiesStats,
  getSoftwareVulnerabilitiesFilters,
} from '../controllers/vulnerabilities.controller';
import { body, param, query } from 'express-validator';
import { validatePagination } from '@root/shared/middleware/validatePagination';

import { hasPermission } from '@shared/middleware/auth-middleware';
import { validateBody } from '@root/shared/middleware/validation-middleware';
import LANGUAGES from '@root/shared/constants/languages';

const router = Router();

router.get('/', hasPermission('read:vulnerabilities.vulnerabilities'), getSoftwareVulnerabilities);

router.get('/search', hasPermission('read:vulnerabilities.vulnerabilities'), searchBySoftwareOrCVE);

router.get(
  '/filters',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getSoftwareVulnerabilitiesFilters
);

router.get(
  '/search-apps-cves',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  validatePagination(10),
  searchDataFromAppsOrCVEs
);

router.get(
  '/filter-app-cve',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  validatePagination(20),
  filterVulnerabilitiesByAppAndCVE
);

router.get(
  '/software/:softwareId',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [param('softwareId').isMongoId()],
  validatePagination(20),
  getSoftwareVulnerabilitiesBySoftwareId
);

router.get(
  '/cves/software',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [
    query('name').isString().notEmpty(),
    query('vendor').optional().isString(),
    query('version').optional().isString(),
  ],
  validatePagination(10),
  getCVEsByOneSoftware
);

router.get(
  '/cves/:cveId',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [param('cveId').isMongoId()],
  validatePagination(20),
  getSoftwareVulnerabilitiesByCVEId
);

router.get(
  '/cves/:cveId/information',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [param('cveId').isString()],
  getInformationByCVEId
);

router.post(
  '/cves/:cveId/explanation',
  [param('cveId').isString(), query('language').isString().isIn(LANGUAGES)],
  validateBody,
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getCveExplanationController
);

router.get(
  '/metrics/patching-cadence',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getMetricsForPatchingCadence
);

router.get(
  '/metrics/stats',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getMetricsForVulnerabilitiesStats
);

router.get(
  '/metrics/distribution',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getMetricsForVulnerabilitiesDistribution
);

router.get(
  '/metrics/top-vulnerable-apps',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getTopVulnerableApps
);

router.get(
  '/metrics/cves/:cveId/affected-hosts',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getMetricForAffectedHostsByCVE
);

router.get(
  '/metrics/hosts/:hostId/distribution',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  getMetricsForVulnerabilitiesDistributionByHost
);

router.get(
  '/hosts/:hostId/grouped',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [param('hostId').isString()],
  validatePagination(10),
  getSoftwareVulnerabilitiesByHostIdGrouped
);

router.get(
  '/hosts/:hostId',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [param('hostId').isString()],
  validatePagination(10),
  getSoftwareVulnerabilitiesByHostId
);

router.post(
  '/software-list',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [body('software').isArray().withMessage('An array of software objects is required')],
  validatePagination(10),
  getCVEsListForEachIntoSoftwareList
);

router.post(
  '/total-software-list',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  [body('software').isArray().withMessage('An array of software objects is required')],
  validatePagination(10),
  getCVEsCountForEachIntoSoftwareList
);

router.post(
  '/export',
  validateBody,
  hasPermission('read:vulnerabilities.vulnerabilities'),
  exportVulnerabilities
);

router.post(
  '/metrics/hosts/:hostId/distribution/export',
  validateBody,
  hasPermission('read:vulnerabilities.vulnerabilities'),
  exportMetricsForVulnerabilitiesDistributionByHost
);

router.post(
  '/metrics/cves/:cveId/affected-hosts/export',
  hasPermission('read:vulnerabilities.vulnerabilities'),
  exportMetricForAffectedHostsByCVE
);

export default router;
