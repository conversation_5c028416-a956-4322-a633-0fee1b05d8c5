import {
  VULNERABILITY_SERVICE_URL,
  VULNERABILITY_SERVICE_CLIENT_ID,
  VULNERABILITY_SERVICE_BASE_API_KEY,
} from '@shared/constants/env';
import VulnerabilityServiceConnector from './vulnServiceConnector';
import { Logger } from '@root/shared/helpers/classes/logger.class';
import { GetAllQuery } from '@root/shared/types';

// Init vulnerability service connector
const vulnerabilityService = new VulnerabilityServiceConnector({
  serviceUrl: VULNERABILITY_SERVICE_URL,
  clientId: VULNERABILITY_SERVICE_CLIENT_ID,
  apiKey: VULNERABILITY_SERVICE_BASE_API_KEY,
});

export const getVulnerabilitiesFilters = async (): Promise<[any, any]> => {
  try {
    const { filter, fields } = await vulnerabilityService.get(`/api/vulns/filters`);
    return [filter, fields];
  } catch (error) {
    Logger.error('Error getting vulnerabilities filters:', error);
    throw error;
  }
};

export const getSoftwareVulnerabilitiesBySoar = async (query: GetAllQuery): Promise<any> => {
  try {
    const { filter = '', limit, offset } = query;
    const prefix = 'input:contains:'; // TODO(Eze): Only for first implementation of search - remove to the second version
    const search = filter.startsWith(prefix) ? filter.slice(prefix.length) : '';

    return await vulnerabilityService.get(`/api/vulns`, { limit, offset, filter: search });
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getSoftwareVulnerabilitiesToExport = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  _sort: string = ''
): Promise<{ data: any[]; meta: { count: number; resources: number; offset: number } }> => {
  try {
    const prefix = 'input:contains:'; // TODO(Eze): Only for first implementation of search - remove to the second version
    const search = filter.startsWith(prefix) ? filter.slice(prefix.length) : '';

    if (limit !== undefined) limit = limit === -1 ? Number.MAX_SAFE_INTEGER : Number(limit);

    const result: { data: []; meta: { count: number; resources: number; offset: number } } =
      await vulnerabilityService.get(`/api/vulns`, {
        limit,
        offset,
        filter: search,
      });

    const { data } = result;

    const processedData = data.map((item: any) => {
      let cvssVersion: string = 'NOT SET';
      let severity: string = 'NOT SET';
      let score: number = 0;

      const metrics = item.metrics;

      if (metrics.cvssMetricV40 && metrics.cvssMetricV40.length > 0) {
        const metric = metrics.cvssMetricV40[0];
        cvssVersion = metric.cvssData.version;
        severity = metric.cvssData.baseSeverity;
        score = metric.cvssData.baseScore;
      } else if (metrics.cvssMetricV31 && metrics.cvssMetricV31.length > 0) {
        const metric = metrics.cvssMetricV31[0];
        cvssVersion = metric.cvssData.version;
        severity = metric.cvssData.baseSeverity;
        score = metric.cvssData.baseScore;
      } else if (metrics.cvssMetricV30 && metrics.cvssMetricV30.length > 0) {
        const metric = metrics.cvssMetricV30[0];
        cvssVersion = metric.cvssData.version;
        severity = metric.cvssData.baseSeverity;
        score = metric.cvssData.baseScore;
      } else if (metrics.cvssMetricV2 && metrics.cvssMetricV2.length > 0) {
        const metric = metrics.cvssMetricV2[0];
        cvssVersion = metric.cvssData.version;
        severity = metric.baseSeverity;
        score = metric.cvssData.baseScore;
      }

      return {
        assetsAffected: item.assetsAffected,
        application: item.application,
        vendor: item.vendor,
        appVersion: item.appVersion,
        cveId: item.cveId,
        description: item.description,
        cvssVersion,
        severity,
        score,
      };
    });

    return {
      data: processedData,
      meta: {
        count: result.meta.count,
        offset: result.meta.offset,
        resources: result.meta.resources,
      },
    };
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const metricsVulnerabilitiesDistributionByHostToExport = async (
  hostId: string
): Promise<{ data: any[] }> => {
  try {
    return await vulnerabilityService.get(`/api/vuln-trends/hosts/${hostId}/distribution/export`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getAffectedHostByCVEForExport = async (
  cveId: string,
  hosts: { name: string; id: string }[] = []
): Promise<{ data: any[] }> => {
  try {
    const response = await vulnerabilityService.post(
      `/api/vuln-trends/cves/${cveId}/affected-hosts/export`,
      { hosts }
    );

    return response;
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const searchVulnerabilitiesBySoftwareOrCVE = async (
  query: Record<string, any>
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/search`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const basicSearchDataFromAppsOrCVEs = async (query: Record<string, any>): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/search-apps-cves`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const filterVulnerabilitiesFromAppAndCVE = async (
  query: Record<string, any>
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/filter-app-cve`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getVulnerabilitiesBySoftwareId = async (
  softwareId: string,
  query: Record<string, any>
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/software/${softwareId}`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getVulnerabilitiesByCVEId = async (
  cveId: string,
  query: Record<string, any>
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/cves/${cveId}`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getFullInformationByCVEId = async (cveId: string): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/cves/${cveId}`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getCVEsBySoftware = async (software: {
  name: string;
  vendor?: string;
  version?: string;
}): Promise<any> => {
  try {
    const { name, vendor, version } = software;

    const queryParams = new URLSearchParams({
      name,
      vendor: vendor || '',
      version: version || '',
    });

    return await vulnerabilityService.get(`/api/vulns/cves/by-software`, queryParams);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getVulnerabilitiesByHostIdGrouped = async (
  hostId: string,
  query: Record<string, any>
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/hosts/${hostId}/grouped`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getVulnerabilitiesByHostId = async (
  hostId: string,
  query: Record<string, any>
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/hosts/${hostId}`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getTotalVulnsByHostId = async (hostId: string): Promise<number> => {
  try {
    return await vulnerabilityService.get(`/api/vulns/hosts/${hostId}/total`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getCVEsListForSoftwareList = async (software: any): Promise<any> => {
  try {
    return await vulnerabilityService.post(`/api/vulns/software-list`, { software });
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getCVEsTotalFromSoftwareList = async (software: any): Promise<any> => {
  try {
    return await vulnerabilityService.post(`/api/vulns/total-software-list`, { software });
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const metricsForPatchingCadence = async (): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vuln-trends/patching-cadence`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const metricsForTopVulnerableApps = async (): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vuln-trends/top-vulnerable-apps`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const metricsForVulnerabilitiesDistribution = async (): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vuln-trends/distribution`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const metricsForVulnerabilitiesStats = async (): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vuln-trends/stats`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getAffectedHostByCVE = async (cveId: string, hostIds: string[] = []): Promise<any> => {
  try {
    const query = new URLSearchParams();
    hostIds.forEach((id) => query.append('hostIds', id));

    return await vulnerabilityService.get(`/api/vuln-trends/cves/${cveId}/affected-hosts`, query);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const metricsForVulnerabilitiesDistributionByHostId = async (
  hostId: string
): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/vuln-trends/hosts/${hostId}/distribution`);
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const getCveExplanation = async (cveId: string, language: string = 'en'): Promise<any> => {
  try {
    return await vulnerabilityService.get(`/api/ai/cves/${cveId}/explanation`, { language });
  } catch (error) {
    Logger.error('Error getting vulnerabilities:', error);
    throw error;
  }
};

export const deleteHostFromVulnService = async (hostId: string): Promise<any> => {
  try {
    return await vulnerabilityService.delete(`/api/vulns/hosts/${hostId}`);
  } catch (error) {
    Logger.error('Error deleting host from vulnerability service:', error);
  }
};
