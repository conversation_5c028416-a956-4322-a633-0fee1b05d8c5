import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import catchAsync from '@root/shared/utils/catch-async';
import {
  getSoftwareVulnerabilitiesBySoar,
  getVulnerabilitiesByCVEId,
  getVulnerabilitiesBySoftwareId,
  searchVulnerabilitiesBySoftwareOrCVE,
  getVulnerabilitiesByHostIdGrouped,
  getVulnerabilitiesByHostId,
  getCVEsListForSoftwareList,
  getCVEsTotalFromSoftwareList,
  getCVEsBySoftware,
  getFullInformationByCVEId,
  basicSearchDataFromAppsOrCVEs,
  filterVulnerabilitiesFromAppAndCVE,
  getSoftwareVulnerabilitiesToExport,
  metricsForPatchingCadence,
  metricsForTopVulnerableApps,
  metricsForVulnerabilitiesDistribution,
  getAffectedHostByCVE,
  metricsForVulnerabilitiesDistributionByHostId,
  metricsVulnerabilitiesDistributionByHostToExport,
  getAffectedHostByCVEForExport,
  getCveExplanation,
  metricsForVulnerabilitiesStats,
  getVulnerabilitiesFilters,
} from '../helpers/vulnerabilities.functions';
import { ServiceResponse } from '@root/shared/models/service-response';
import { exportFileOf } from '@root/services/Export/controllers/export.controllers';
import { getClientsFromRport } from '@root/services/Rport/helpers/functions';
import { errors } from '@root/shared/utils/app-errors';

/////////////////// Vulnerabilities gral. ///////////////////

export const getSoftwareVulnerabilitiesFilters: RequestHandler = catchAsync(async (_, res) => {
  const [filter, fields] = await getVulnerabilitiesFilters();

  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getSoftwareVulnerabilities: RequestHandler = catchAsync(async (req, res) => {
  try {
    const {
      data,
      meta: { count, offset },
    } = await getSoftwareVulnerabilitiesBySoar(req.query);

    return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0)).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const searchBySoftwareOrCVE: RequestHandler = catchAsync(async (req, res) => {
  try {
    const {
      data,
      meta: { count, offset },
    } = await searchVulnerabilitiesBySoftwareOrCVE(req.query);

    return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0)).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const searchDataFromAppsOrCVEs: RequestHandler = catchAsync(async (req, res) => {
  try {
    const data = await basicSearchDataFromAppsOrCVEs(req.query);

    return ServiceResponse.get(data).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const filterVulnerabilitiesByAppAndCVE: RequestHandler = catchAsync(async (req, res) => {
  try {
    const {
      data,
      meta: { count, offset },
    } = await filterVulnerabilitiesFromAppAndCVE(req.query);

    return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0)).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getSoftwareVulnerabilitiesBySoftwareId: RequestHandler = catchAsync(
  async (req, res) => {
    try {
      const softwareId = req.params.softwareId;

      const {
        data,
        meta: { count, offset },
      } = await getVulnerabilitiesBySoftwareId(softwareId, req.query);

      return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0)).send(res);
    } catch (_) {
      throw errors.not_found('Vulnerabilities');
    }
  }
);
export const getSoftwareVulnerabilitiesByCVEId: RequestHandler = catchAsync(async (req, res) => {
  try {
    const cveId = req.params.cveId;

    const {
      data,
      meta: { count, offset },
    } = await getVulnerabilitiesByCVEId(cveId, req.query);

    return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0)).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getInformationByCVEId: RequestHandler = catchAsync(async (req, res) => {
  try {
    const cveId = req.params.cveId;

    const result = await getFullInformationByCVEId(cveId);

    return ServiceResponse.get(result).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getSoftwareVulnerabilitiesByHostIdGrouped: RequestHandler = catchAsync(
  async (req, res) => {
    try {
      const hostId = req.params.hostId;

      const {
        data,
        meta: { count, offset, lastInventoryUpdate },
      } = await getVulnerabilitiesByHostIdGrouped(hostId, req.query);

      return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0), { lastInventoryUpdate }).send(
        res
      );
    } catch (_) {
      throw errors.not_found('Vulnerabilities');
    }
  }
);

export const getSoftwareVulnerabilitiesByHostId: RequestHandler = catchAsync(async (req, res) => {
  try {
    const hostId = req.params.hostId;

    const {
      data,
      meta: { count, offset },
    } = await getVulnerabilitiesByHostId(hostId, req.query);

    return ServiceResponse.get(data, +(count ?? 0), +(offset ?? 0)).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getCVEsListForEachIntoSoftwareList: RequestHandler = catchAsync(async (req, res) => {
  try {
    const softwareList = req.body.software;

    const cvesList = await getCVEsListForSoftwareList(softwareList);

    return ServiceResponse.get(cvesList).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getCVEsCountForEachIntoSoftwareList: RequestHandler = catchAsync(async (req, res) => {
  try {
    const softwareList = req.body.software;

    const cvesList = await getCVEsTotalFromSoftwareList(softwareList);

    return ServiceResponse.get(cvesList).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getCVEsByOneSoftware: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { name, vendor, version } = req.query as {
      name: string;
      vendor?: string;
      version?: string;
    };
    const cvesList = await getCVEsBySoftware({ name, vendor, version });

    return ServiceResponse.get(cvesList).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

/////////////////// Exports ///////////////////
export const exportVulnerabilities: RequestHandler = catchAsync(async (req, res) => {
  try {
    return await exportFileOf(req, res, getSoftwareVulnerabilitiesToExport, ({ data }) => data);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const exportMetricsForVulnerabilitiesDistributionByHost: RequestHandler = catchAsync(
  async (req, res) => {
    try {
      const { hostId } = req.params;

      return await exportFileOf(
        req,
        res,
        () => metricsVulnerabilitiesDistributionByHostToExport(hostId),
        ({ data }) => data
      );
    } catch (_) {
      throw errors.not_found('Vulnerabilities');
    }
  }
);

export const exportMetricForAffectedHostsByCVE: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { cveId } = req.params;

    const { data } = await getClientsFromRport();

    const hosts: { name: string; id: string }[] = data.map(({ name, id }) => ({ name, id }));

    return await exportFileOf(
      req,
      res,
      () => getAffectedHostByCVEForExport(cveId, hosts),
      ({ data }) => data
    );
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

/////////////////// Metrics ///////////////////
export const getMetricsForPatchingCadence: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { data } = await metricsForPatchingCadence();

    return ServiceResponse.get(data).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getTopVulnerableApps: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { data } = await metricsForTopVulnerableApps();

    return ServiceResponse.get(data).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getMetricsForVulnerabilitiesDistribution: RequestHandler = catchAsync(
  async (req, res) => {
    try {
      const { data } = await metricsForVulnerabilitiesDistribution();
      return ServiceResponse.get(data).send(res);
    } catch (_) {
      throw errors.not_found('Vulnerabilities');
    }
  }
);

export const getMetricsForVulnerabilitiesStats: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { data } = await metricsForVulnerabilitiesStats();
    return ServiceResponse.get(data).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getMetricForAffectedHostsByCVE: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { cveId } = req.params;

    const { data } = await getClientsFromRport();

    const hostIds: string[] = data.map((client) => client.id);

    const { data: responseVulnsData } = await getAffectedHostByCVE(cveId, hostIds);

    return ServiceResponse.get(responseVulnsData).send(res);
  } catch (_) {
    throw errors.not_found('Vulnerabilities');
  }
});

export const getMetricsForVulnerabilitiesDistributionByHost: RequestHandler = catchAsync(
  async (req, res) => {
    try {
      const { hostId } = req.params;

      const { data } = await metricsForVulnerabilitiesDistributionByHostId(hostId);

      return ServiceResponse.get(data).send(res);
    } catch (_) {
      throw errors.not_found('Vulnerabilities');
    }
  }
);

export const getCveExplanationController: RequestHandler = catchAsync(async (req, res) => {
  try {
    const { cveId } = req.params;
    const { language } = req.query as { language: string };

    const { data } = await getCveExplanation(cveId, language);

    return ServiceResponse.post(data).send(res);
  } catch {
    throw errors.not_found('Vulnerabilities');
  }
});
