import { Request<PERSON><PERSON><PERSON> } from 'express';

import { HostFixStatus } from '../schemas';
import { RportClient } from '@services/Rport/schemas';

import { GetAllQuery } from '@shared/types';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';

import { exportFileOf } from '@services/Export/controllers';
import { getAndValidateClientsConnected } from '@services/Rport/helpers/utils/rport-platform.utils';

import { hostFixStatesFilters, getHostFixStatus } from '../helpers/functions';
import { ServiceResponse } from '@root/shared/models/service-response';

export const getHostFixStatesFilters: RequestHandler = catchAsync(async (req, res) => {
  const [filters, fields] = hostFixStatesFilters();
  res.status(200).json({ filter: filters, fields });
});

export const getAllHostFixStates: RequestHandler = catchAsync(async (req, res) => {
  const { limit, offset, filter, sort } = req.query as GetAllQuery;

  const responseData = await getHostFixStatus(filter, limit, offset, sort);

  return res.status(200).json(responseData);
});

export const exportHostFixStates: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, getHostFixStatus, ({ data }) => data);
});

export const getHostFixStatusById: RequestHandler = catchAsync(async (req, res) => {
  // Getting host Id from request params
  const { hostId } = req.params;
  if (!hostId) throw errors.not_found('Host Id');

  // Get Host from DB
  const host = await RportClient.findOne({ rportId: hostId, deleted: false });

  // Check if host is enabled and not deleted
  if (!host) throw errors.not_found('Host');
  if (!host.enabled) throw errors.not_enabled('Host');

  // Getting host connection status
  const { clientIdsConnected } = await getAndValidateClientsConnected([hostId]);

  // Get HostFixStatus from DB
  const hostFixStatus = await HostFixStatus.find({ host: hostId })
    .populate({
      path: 'fix',
      select: 'name importance',
    })
    .select('status fix updatedAt createdAt');

  // Separate the data into two arrays based on the status (pending, applied)
  const pendingFixes = hostFixStatus.filter((fix) => fix.status === 'pending');
  const appliedFixes = hostFixStatus.filter((fix) => fix.status === 'applied');

  // Return the data

  res.status(200).json({
    hostId: host.rportId,
    hostname: host.name,
    address: host.address,
    osKernel: host.osKernel,
    os: host.os,
    osVersion: host.osVersion,
    connection_status: clientIdsConnected.includes(hostId) ? 'connected' : 'disconnected',
    batutaId: host.id,
    pendingFixes,
    appliedFixes,
  });
});

// Dashboard Routes
export const getMissingFixesCount: RequestHandler = catchAsync(async (_, res) => {
  const aggregation = await HostFixStatus.aggregate([
    // Match only pending fixes
    { $match: { status: 'pending' } },
    // Join with the AptFix collection to get the importance field
    {
      $lookup: {
        from: 'aptfixes',
        localField: 'fix',
        foreignField: '_id',
        as: 'fix',
      },
    },
    { $unwind: '$fix' },
    // Join with RportClient to get active host
    {
      $lookup: {
        from: 'rportclients',
        localField: 'host',
        foreignField: 'rportId',
        as: 'client',
      },
    },
    { $unwind: '$client' },
    {
      $match: {
        'client.enabled': true,
        'client.deleted': false,
      },
    },
    // Group by importance and count how many fixes by level
    {
      $group: {
        _id: '$fix.importance',
        count: { $sum: 1 },
      },
    },
  ]);

  const result = {
    low: 0,
    medium: 0,
    high: 0,
  };

  aggregation.forEach(({ _id, count }) => {
    if (_id in result) {
      result[_id as 'low' | 'medium' | 'high'] = count;
    }
  });

  return ServiceResponse.get({ ...result }).send(res);
});

export const getMissingHighFixesCount: RequestHandler = catchAsync(async (_, res) => {
  const aggregation = await HostFixStatus.aggregate([
    // Match pending fixes
    { $match: { status: 'pending' } },
    // Lookup the fix to get importance
    {
      $lookup: {
        from: 'aptfixes',
        localField: 'fix',
        foreignField: '_id',
        as: 'fix',
      },
    },
    { $unwind: '$fix' },
    // Filter only high importance
    { $match: { 'fix.importance': 'high' } },
    // Join with only active hosts
    {
      $lookup: {
        from: 'rportclients',
        localField: 'host',
        foreignField: 'rportId',
        as: 'client',
      },
    },
    { $unwind: '$client' },
    {
      $match: {
        'client.enabled': true,
        'client.deleted': false,
      },
    },
    // Group by host
    {
      $group: {
        _id: '$host',
      },
    },
    //Count the number of unique hosts
    {
      $count: 'count',
    },
  ]);

  const count = aggregation[0]?.count || 0;

  return ServiceResponse.get({ count }).send(res);
});

export const getTopMissingFixesHosts: RequestHandler = catchAsync(async (_, res) => {
  // Aggregate the missing fixes directly in MongoDB with a lookup to hosts information:
  // gets the top hosts with pending fixes,
  // joins each one with its host info,
  // filters out deleted or disabled hosts,
  // counts how many fixes each has,
  // returns the top results with their name.
  const hostFixesAggregation = await HostFixStatus.aggregate([
    { $match: { status: 'pending' } },
    {
      $lookup: {
        from: 'rportclients',
        localField: 'host',
        foreignField: 'rportId',
        as: 'client',
      },
    },
    { $unwind: '$client' },
    {
      $match: {
        'client.enabled': true,
        'client.deleted': false,
      },
    },
    { $group: { _id: '$host', count: { $sum: 1 }, hostname: { $first: '$client.name' } } },
    { $sort: { count: -1, hostname: 1 } },
    {
      $project: {
        _id: 0,
        id: '$_id',
        hostname: 1,
        count: 1,
      },
    },
  ]);

  return ServiceResponse.get({ data: hostFixesAggregation.slice(0, 20) }).send(res);
});
