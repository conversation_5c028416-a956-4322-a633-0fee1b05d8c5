import { Types } from 'mongoose';
import { platform } from '../types';

export type Severity = 'Critical' | 'High' | 'Medium' | 'Low' | 'Informational';

export interface DetectionDataModel {
  action_taken: string;
  command_line: string;
  detection_id: string;
  file_name: string;
  file_path: string;
  grand_parent_command_line: string;
  grand_parent_image_file_name: string;
  ioc_type: string;
  ioc_value: string;
  md5_hash: string;
  objective: string;
  parent_command_line: string;
  parent_image_file_name: string;
  severity: Severity;
  sha256_hash: string;
  sha_256: string;
  tactic: string;
  technique: string;
  url: string;
  user_name: string;
  bios_manufacturer: string;
  bios_version: string;
  domain: any;
  external_ip: string;
  groups: string;
  hostname: string;
  instance_id: any;
  local_ip: string;
  mac_address: string;
  os_version: string;
  ou: any;
  platform: platform;
  product_type: string;
  sensor_id: string;
  sensor_version: string;
  service_provider: any;
  service_provider_account_id: any;
  site_name: any;
  system_manufacturer: string;
  system_product: string;
  tags: any;
}

export interface DetectionMetaModel {
  event_reference_url: string;
  timestamp: number;
  trigger_name: string;
  trigger_category?: string;
  workflow_id: string;
}

export interface DetectionModel {
  data: DetectionDataModel;
  meta: DetectionMetaModel;
  detectionId: string;
  console: Types.ObjectId;
}

export interface CrowdstrikePayloadModel extends DetectionModel {
  type: crowdstrikeBody;
}

type crowdstrikeBody = 'new' | 'update' | 'policy';
