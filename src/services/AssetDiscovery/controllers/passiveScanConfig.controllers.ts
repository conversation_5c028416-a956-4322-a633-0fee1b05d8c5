import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { startSession, Types } from 'mongoose';

import { PassiveScanConfig, PassiveScanConfigDocument } from '../schemas/passiveScanConfig.schema';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';
import { ServiceResponse } from '@shared/models/service-response';

import { startPassiveScanProcess } from '../helpers/functions';
import { RportGroup } from '@root/services/Rport/schemas';

export const getPassiveScanConfig: RequestHandler = catchAsync(async (_, res) => {
  const passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
  if (!passiveScanConfig) throw errors.not_found('Passive Scan Config');

  return ServiceResponse.get(passiveScanConfig).send(res);
});

export const updatePassiveScanConfig: RequestHandler = catchAsync(async (req, res) => {
  const { interval, batchSize, groups } = req.body as {
    interval?: number;
    batchSize?: number;
    groups?: Types.ObjectId[];
  };

  // Validate interval (min of 1 hour max of 1 every 7 days)
  if (interval && (interval < 3600 || interval > 604800)) {
    throw errors.not_valid('Interval', 'Interval must be between 1 hour and 7 days');
  }

  // Validate batch size (min of 100 max of 1000)
  if (batchSize && (batchSize < 100 || batchSize > 1000)) {
    throw errors.not_valid('Batch Size', 'Batch size must be between 100 and 1000');
  }

  // Start MongoDB session for transaction
  const session = await startSession();
  session.startTransaction();

  try {
    // update the singleton instance with the new values
    let passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
    if (!passiveScanConfig) throw errors.not_found('Passive Scan Config');

    // If none of the values were provided, return the current config
    if (!interval && !batchSize && !groups) {
      return ServiceResponse.get(passiveScanConfig).send(res);
    }

    // Generate a object with the new values
    const updateFields: Partial<PassiveScanConfigDocument> = {};

    if (interval && passiveScanConfig.interval !== interval) updateFields.interval = interval;
    if (batchSize && passiveScanConfig.batchSize !== batchSize) updateFields.batchSize = batchSize;

    // Handle group update
    if (groups !== undefined && groups !== null) {
      // Map existing affectedGroups to string; treat null as an empty array
      const existingGroups: string[] =
        passiveScanConfig.groups.map((group) => group.toString()) ?? [];

      // Map new groups to string (even if it's an empty array, meaning removal of all groups)
      const newGroups: string[] = groups.map((group) => group.toString());

      // Compare arrays for equality (ignoring order)
      const existingGroupsSet = new Set(existingGroups);
      const areEqual =
        newGroups.length === existingGroups.length &&
        newGroups.every((group) => existingGroupsSet.has(group));

      if (!areEqual) {
        // Validate that each provided group exists in the database
        for (const group of groups) {
          const existingGroup = await RportGroup.findById(group).session(session);
          if (!existingGroup) throw errors.not_found('Group');
        }
        // Update groups with the new groups array (could be empty)
        updateFields.groups = groups;
      }
    }

    // Update the singleton instance
    passiveScanConfig = await PassiveScanConfig.findOneAndUpdate(
      passiveScanConfig._id,
      { ...updateFields, updatedBy: req.user?.email },
      { new: true }
    );
    if (!passiveScanConfig) throw errors.not_found('Passive Scan Config');

    // Commit the transaction
    await session.commitTransaction();
    await session.endSession();

    // Return the updated singleton instance
    return ServiceResponse.patch(passiveScanConfig).send(res);
  } catch (error) {
    await session.abortTransaction();
    await session.endSession();
    throw error;
  }
});

// Toggle the status of the passive scan
export const togglePassiveScanStatus: RequestHandler = catchAsync(async (req, res) => {
  const { status } = req.body as { status: boolean };

  let passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
  if (!passiveScanConfig) throw errors.not_found('Passive Scan Config');

  const updatedFields: Partial<PassiveScanConfigDocument> = {
    enabled: status,
    updatedBy: req.user?.email,
  };

  // If the scan is being enabled, calculate next run time
  if (status) {
    const now = new Date();
    const nextRunTime = new Date(now.getTime() + passiveScanConfig.interval * 1000);
    updatedFields.nextExecution = nextRunTime;
  }

  // Toggle the status
  passiveScanConfig = await PassiveScanConfig.findOneAndUpdate(
    passiveScanConfig._id,
    updatedFields,
    { new: true }
  );

  if (!passiveScanConfig) throw errors.not_found('Passive Scan Config');

  // Return the updated singleton instance
  return ServiceResponse.patch(passiveScanConfig).send(res);
});

export const runPassiveScan: RequestHandler = catchAsync(async (_, res) => {
  // Check if the passive scan config exists
  const passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
  if (!passiveScanConfig) throw errors.not_found('Passive Scan Config');

  // Check if is already running
  if (passiveScanConfig.lockStatus) throw errors.already_running('Passive Scan');

  // Run the passive scan
  startPassiveScanProcess(
    passiveScanConfig._id,
    passiveScanConfig.batchSize,
    passiveScanConfig.interval,
    passiveScanConfig.groups
  );

  res.status(204).json({});
});
