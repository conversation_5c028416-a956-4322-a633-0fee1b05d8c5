import { Re<PERSON><PERSON><PERSON><PERSON> } from 'express';

import { RportClient } from '@services/Rport/schemas';
import { ScannedHost } from '../schemas/scannedHost.schema';
import {
  MANAGED_HOST,
  NON_MANAGEABLE_HOST,
  NON_MANAGED_HOST,
  ScannedHostMetricsTimeline,
} from '../helpers/models';
import { Network } from '../schemas';

import { GetAllQuery } from '@shared/types';

import { exportFileOf } from '@services/Export/controllers';

import { ServiceResponse } from '@shared/models/service-response';
import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';
import { TLanguage } from '@root/shared/types/languages.types';

import { getHostById } from '@services/Rport/helpers/functions/client.functions';

import {
  getScannedHostFiltersFormated,
  calculateScannedHostMetrics,
  getLast30DaysScannedHostMetrics,
  getScannedHostFromDb,
  generateAISummary,
} from '../helpers/functions/';

export const getScannedHostFilters: RequestHandler = catchAsync(async (_, res) => {
  const [filter, fields] = await getScannedHostFiltersFormated();
  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getScannedHosts: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit = 100, offset = 0, sort } = req.query as GetAllQuery;

  const { data, meta } = await getScannedHostFromDb(filter, limit, offset, sort);

  return ServiceResponse.get(data, meta.total, +(meta.offset ?? 0)).send(res);
});

export const exportScannedHosts: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, getScannedHostFromDb, ({ data }) => data);
});

export const getScannedHostById: RequestHandler = catchAsync(async (req, res) => {
  // Get the scanned host id from the request
  const scannedHostId = req.params.id;

  // Get the scheduled report
  const scannedHost = await ScannedHost.findById(scannedHostId).populate('networks', 'name cidr');
  if (!scannedHost) throw errors.not_found('Scanned Host');

  // Populate the scanned host seen information (seenBy) lastSeen and firstSeen
  const firstSeenBy = await RportClient.findOne({
    rportId: { $in: scannedHost.firstSeen.seenBy },
  }).select('name');

  const lastSeenBy = await RportClient.findOne({
    rportId: { $in: scannedHost.lastSeen.seenBy },
  }).select('name');

  // Replace the seenBy field with the populated information
  scannedHost.firstSeen.seenBy = firstSeenBy ? firstSeenBy.name : 'N/A';
  scannedHost.lastSeen.seenBy = lastSeenBy ? lastSeenBy.name : 'N/A';

  // Check if the scanned host is managed
  if (scannedHost.status === MANAGED_HOST) {
    const rportId = scannedHost.hostId as string;
    const existingHost = await getHostById(rportId);
    if (existingHost) {
      // Replace hostId with the existing host information
      const scannedHostManaged = { ...scannedHost.toObject(), hostId: existingHost };

      return ServiceResponse.get(scannedHostManaged).send(res);
    }
  }

  // Return the scanned host
  return ServiceResponse.get(scannedHost).send(res);
});

export const updateScannedHostStatusById: RequestHandler = catchAsync(async (req, res) => {
  // Get the scanned host id from the request
  const scannedHostId = req.params.id;

  // Get the scanned host
  const scannedHost = await ScannedHost.findById(scannedHostId);
  if (!scannedHost) throw errors.not_found('Scanned Host');
  if (scannedHost.status === MANAGED_HOST) {
    throw errors.not_valid('Scanned Host', 'Scanned host is managed by Batuta');
  }

  // Get the status from the request
  const { status } = req.body as { status: typeof NON_MANAGED_HOST | typeof NON_MANAGEABLE_HOST };

  // Validate if the status is different from the current status
  if (status === scannedHost.status) {
    throw errors.not_valid('Scanned Host', 'Status is the same as the current status');
  }

  // Generate the audit event by the user
  const auditEvent = { date: new Date(), status: status, updatedBy: req.user?.email };

  // Update the scanned host status
  const updatedScannedHost = await ScannedHost.findByIdAndUpdate(
    scannedHostId,
    { status, $push: { audit: auditEvent } },
    { new: true }
  ).populate('networks', 'name cidr');

  if (!updatedScannedHost) throw errors.not_found('Scanned Host');

  return ServiceResponse.get(updatedScannedHost).send(res);
});

export const updateManyScannedHostStatus: RequestHandler = catchAsync(async (req, res) => {
  // Get the status from the request
  const { status, ids } = req.body as {
    status: typeof NON_MANAGED_HOST | typeof NON_MANAGEABLE_HOST;
    ids: string[];
  };

  // Get the inverse of the given status
  const inverseStatus = status === 'non_manageable' ? 'non_managed' : 'non_manageable';

  // Get all scanned hosts with the given ids
  const scannedHosts = await ScannedHost.find({ _id: { $in: ids }, status: inverseStatus });

  // Update the scanned hosts with the given status and audit event
  await ScannedHost.updateMany(
    { _id: { $in: scannedHosts.map((host) => host._id) } },
    {
      status,
      $push: {
        audit: {
          date: new Date(),
          status,
          updatedBy: req.user?.email,
        },
      },
    }
  );

  return res.status(204).send({});
});

export const deleteScannedHostById: RequestHandler = catchAsync(async (req, res) => {
  // Get the scanned host id from the request
  const scannedHostId = req.params.id as string;

  // Get the scanned host
  const scannedHost = await ScannedHost.findById(scannedHostId);
  if (!scannedHost) throw errors.not_found('Scanned Host');

  // Check if the scanned host is managed
  if (scannedHost.status === MANAGED_HOST) {
    throw errors.not_valid('Scanned Host', 'Scanned host is managed by Batuta');
  }

  // Delete the scanned host
  await ScannedHost.findByIdAndDelete(scannedHostId);

  return res.status(204).send({});
});

export const deleteManyScannedHosts: RequestHandler = catchAsync(async (req, res) => {
  // Get the scanned host ids from the request
  const { ids } = req.body as { ids: string[] };

  // Filter out the scanned hosts that are managed
  const scannedHosts = await ScannedHost.find({ _id: { $in: ids }, status: { $ne: MANAGED_HOST } });

  // Delete the scanned hosts
  await ScannedHost.deleteMany({ _id: { $in: scannedHosts.map((host) => host._id) } });

  return res.status(204).send({});
});

// Dashboard Routes
export const getScannedHostDistribution: RequestHandler = catchAsync(async (req, res) => {
  // Get networkIds from request query
  const { networkId } = req.query as { networkId?: string | string[] };

  let validNetworks: string[] = [];

  // check if networkId is an string and push it to an array
  if (networkId) {
    // Push the networkId to an array if it's not already an array
    const rawNetworkIds = Array.isArray(networkId) ? networkId : [networkId];

    // Validate if the networks exist
    const networks = await Network.find({ _id: { $in: rawNetworkIds } });

    // Get the valid networks
    validNetworks = networks.map((network) => network._id.toString());
  }

  // Get the scanned host distribution using the reusable function
  const formattedScannedHostDistribution = await calculateScannedHostMetrics(validNetworks);

  // Return the scanned host distribution
  return ServiceResponse.get(formattedScannedHostDistribution).send(res);
});

export const getScannedHostHistoricalMetrics: RequestHandler = catchAsync(async (req, res) => {
  // Get networkIds from request query
  const { networkId } = req.query as { networkId?: string | string[] };

  let validNetworks: string[] = [];

  // check if networkId is an string and push it to an array
  if (networkId) {
    // Push the networkId to an array if it's not already an array
    const rawNetworkIds = Array.isArray(networkId) ? networkId : [networkId];

    // Validate if the networks exist
    const networks = await Network.find({ _id: { $in: rawNetworkIds } });

    // Get the valid networks
    validNetworks = networks.map((network) => network._id.toString());
  }

  // Prepare the result structure with properly typed arrays
  const metrics: ScannedHostMetricsTimeline = await getLast30DaysScannedHostMetrics(validNetworks);

  // Return the formatted metrics
  return ServiceResponse.get(metrics).send(res);
});

export const createAISummary: RequestHandler = catchAsync(async (req, res) => {
  const assetId = req.params.assetId as string;
  const { language } = req.query as { language: TLanguage };

  // Return ai summary for the asset
  const { data } = await generateAISummary(assetId, language);
  return ServiceResponse.get({ data }).send(res);
});
