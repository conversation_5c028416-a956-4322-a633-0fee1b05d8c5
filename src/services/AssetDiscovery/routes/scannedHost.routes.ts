// Dependencies
import { Router } from 'express';
import { body, param, query } from 'express-validator';

// Controllers
import {
  getScannedHostFilters,
  getScannedHosts,
  getScannedHostById,
  updateScannedHostStatusById,
  updateManyScannedHostStatus,
  getScannedHostDistribution,
  getScannedHostHistoricalMetrics,
  exportScannedHosts,
  deleteScannedHostById,
  deleteManyScannedHosts,
  createAISummary,
} from '../controllers';

// Middlewares
import { hasPermission } from '@shared/middleware/auth-middleware';
import { validateBody } from '@shared/middleware/validation-middleware';

// Constants
import { NON_MANAGEABLE_HOST, NON_MANAGED_HOST } from '../helpers/models';
import LANGUAGES from '@root/shared/constants/languages';

const router = Router();

router.get(
  '/',
  hasPermission('read:asset-discovery.scanned-hosts'),
  [
    query('limit').isFloat({ min: 1, max: 500 }).default(100),
    query('offset').isFloat({ min: 0 }).default(0),
    query('filter').isString().optional({ values: 'null' }),
    query('sort').isString().optional({ values: 'null' }),
    query('networkId').isMongoId().optional(),
  ],
  validateBody,
  getScannedHosts
);

router.get('/filters', hasPermission('read:asset-discovery.scanned-hosts'), getScannedHostFilters);
router.post('/export', hasPermission('read:asset-discovery.scanned-hosts'), exportScannedHosts);

router.get(
  '/distribution',
  hasPermission('read:asset-discovery.scanned-hosts'),
  [query('networkId').isMongoId().optional()],
  validateBody,
  getScannedHostDistribution
);

router.get(
  '/historical-metrics',
  hasPermission('read:asset-discovery.scanned-hosts'),
  [query('networkId').isMongoId().optional()],
  validateBody,
  getScannedHostHistoricalMetrics
);

router.post(
  '/delete:bulk',
  hasPermission('delete:asset-discovery.scanned-hosts'),
  [body('ids').isArray({ min: 1, max: 500 })],
  validateBody,
  deleteManyScannedHosts
);

router.patch(
  '/status:bulk',
  hasPermission('update:asset-discovery.scanned-hosts'),
  [
    body('status').isString().isIn([NON_MANAGED_HOST, NON_MANAGEABLE_HOST]),
    body('ids').isArray({ min: 1, max: 500 }).optional(),
  ],
  validateBody,
  updateManyScannedHostStatus
);

router
  .route('/:id')
  .get(
    hasPermission('read:asset-discovery.scanned-hosts'),
    [param('id').isMongoId()],
    validateBody,
    getScannedHostById
  )
  .delete(
    hasPermission('delete:asset-discovery.scanned-hosts'),
    [param('id').isMongoId()],
    validateBody,
    deleteScannedHostById
  );

router.patch(
  '/:id/status',
  hasPermission('update:asset-discovery.scanned-hosts'),
  [
    param('id').isMongoId(),
    body('status').isString().isIn([NON_MANAGED_HOST, NON_MANAGEABLE_HOST]),
  ],
  validateBody,
  updateScannedHostStatusById
);

router.post(
  '/:assetId/ai-summary',
  hasPermission('read:asset-discovery.scanned-hosts'),
  [param('assetId').isMongoId(), query('language').isString().isIn(LANGUAGES)],
  validateBody,
  createAISummary
);

export default router;
