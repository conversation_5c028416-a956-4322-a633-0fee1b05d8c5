import axios from 'axios';

import { MacVendorModel } from '../models';

import { Logger } from '@shared/helpers/classes/logger.class';

import { MAC_VENDOR_API_URL } from '@shared/constants/env';

const customAxios = axios.create({
  baseURL: MAC_VENDOR_API_URL,
});

export async function lookupMacAddress(mac: string): Promise<MacVendorModel> {
  if (!MAC_VENDOR_API_URL) {
    return {
      company: '',
      addressL1: '',
      addressL2: '',
      addressL3: '',
      country: '',
      type: '',
    };
  }

  try {
    const response = await customAxios.get(`/v2/${mac}`);

    const macVendorResponse = response.data?.[0] as MacVendorModel;

    return macVendorResponse;
  } catch (error: any) {
    Logger.error(`Error looking up MAC address ${mac}: ${error.message}`);
    throw error;
  }
}
