import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

import { TLanguage } from '@root/shared/types/languages.types';
import { OPENAI_API_KEY } from '@shared/constants/env';

const BASE_PROMPT = `Act as a cybersecurity AI assistant that analyzes a discovered asset based on network scan data.

Asset information:
- MAC Address: {MACAddress}
- Vendor: {vendor}
- IPv4 Address: {ipV4}
- Details: {assetSummary}

The response must be provided in {language}.

Guidelines:
- Analyze the MAC address and infer the probable manufacturer if possible.
- Remember that the MAC address vendor typically indicates the network interface manufacturer, not necessarily the device manufacturer.
- Explain whether the MAC address suggests a virtualized, randomized or physical device.
- Use the MAC Type to assist your reasoning.
- Do not include any "Conclusion" section or summary paragraph at the end. Only provide the technical findings.
- Do not include generic statements such as "the information is limited", "identification is inconclusive", "further analysis is needed" or similar disclaimers. Focus only on the technical interpretation of the provided data.
- Do not include any personal opinions or subjective statements.
- Do not include any references to the AI model or its capabilities.
- Do not include any greetings, introductions, or conclusions.
- Keep the response technical, short, and focused for IT security analysts.`;

export class AIService {
  private static instance: AIService;
  private llm: ChatOpenAI;

  private constructor() {
    this.llm = new ChatOpenAI({
      temperature: 0.4,
      model: 'gpt-4o',
      openAIApiKey: OPENAI_API_KEY,
    });
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  public async generateAssetSummary(
    MACAddress: string,
    vendor: string | null,
    ipV4: string | null,
    assetSummary: string,
    language: TLanguage
  ): Promise<any> {
    const promptTemplate = PromptTemplate.fromTemplate(BASE_PROMPT);

    // Format the prompt with the provided data
    const prompt = await promptTemplate.format({
      language: this.getLanguageName(language),
      MACAddress,
      vendor: !vendor || vendor.trim() === '' ? 'Unknown' : vendor,
      ipV4: ipV4 || 'Not Available',
      assetSummary,
    });

    // Invoke the LLM with the formatted prompt
    const { content } = await this.llm.invoke([{ role: 'user', content: prompt }]);
    return content;
  }

  private getLanguageName(language: TLanguage): string {
    if (language === 'es') return 'Spanish';
    return 'English';
  }
}
