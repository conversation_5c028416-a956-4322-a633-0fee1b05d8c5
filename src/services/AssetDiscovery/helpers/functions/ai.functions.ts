import { errors } from '@root/shared/utils/app-errors';
import { TLanguage } from '@root/shared/types/languages.types';

import { AiAsset, ScannedHost } from '../../schemas';
import { AIService } from '../services/ai.service';
import { ScannedHostModel } from '../models';

export const generateAISummary = async (
  assetId: string,
  language: TLanguage
): Promise<{ data: string }> => {
  try {
    const document = await AiAsset.findOne({ assetId }).lean();

    if (document) {
      const existing = document.content.find(({ lang }) => lang === language);
      if (existing) return { data: existing.value };
    }
    const hostInformation = await ScannedHost.findById(assetId).lean<ScannedHostModel>();
    if (!hostInformation) throw errors.not_found('Scanned Host');

    const summary = prepareSummaryForAI(hostInformation);

    const aiService = AIService.getInstance();

    const newContent = await aiService.generateAssetSummary(
      hostInformation.mac,
      hostInformation.MacVendor,
      hostInformation.IPv4 ? hostInformation.IPv4.join(', ') : null,
      summary,
      language
    );

    // if not exist language content, update or create it
    if (document) {
      await AiAsset.updateOne(
        { assetId },
        { $push: { content: { lang: language, value: newContent } } }
      );
    } else {
      await AiAsset.create({
        assetId,
        content: [{ lang: language, value: newContent }],
        createdAt: new Date(),
      });
    }

    return { data: newContent };
  } catch (err) {
    throw err;
  }
};

export const prepareSummaryForAI = (asset: ScannedHostModel): string => {
  const macType = asset.MacType ?? 'Unknown';

  return `MAC Type: ${macType}.`;
};
