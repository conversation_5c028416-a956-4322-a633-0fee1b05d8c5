import { Types } from 'mongoose';

import { RportClient } from '@services/Rport/schemas';
import { RportMacAddress } from '@services/Rport/schemas/rport-mac-address.schema';
import { ScannedHost } from '../../schemas/scannedHost.schema';
import { Network } from '../../schemas/network.schema';

import { SelectRelative, FilterType, RelativeType, StringRelative } from '@shared/models';
import {
  ScanResultsModel,
  MANAGED_HOST,
  NON_MANAGED_HOST,
  NON_MANAGEABLE_HOST,
  ScannedHostAuditEvent,
  ScannedHostMetrics,
  MacType,
  ScannedHostStatus,
} from '../models';

import { Logger } from '@shared/helpers/classes/logger.class';
import { buildExtraFilters } from '@shared/helpers/classes/schema-utils.class';

import { getNetworksFromScan } from './network.functions';
import { lookupMacAddress } from '../services/mac-vendor.service';

export const getScannedHostFromDb = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = ''
) => {
  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = ScannedHost.parseFilter(filter, sort, [
    'networks',
    'IPv4',
    'IPv6',
  ]);
  const extra = buildExtraFilters(extraFilter);

  // Limits is equal to -1 only when the request is a export
  if (limit == -1) {
    limit = 0; // No limit
  } else {
    limit = limit ?? 100;
  }

  const [filteredResults, filteredResultsCount] = await Promise.all([
    ScannedHost.find({ $and: [query, extra] })
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? '-createdAt')
      .limit(limit)
      .skip(offset ?? 0)
      .populate('networks', 'name'),
    ScannedHost.countDocuments({ $and: [query, extra] }),
  ]);

  return {
    meta: { total: filteredResultsCount, resources: filteredResults.length, offset: +offset },
    data: filteredResults,
  };
};

export const getScannedHostFiltersFormated = async () => {
  // Filters that are not needed to be shown
  const skippedFilter = ['protected', 'deleted', 'enabled', 'hostId'];

  // Get list of networks for filter dropdown
  const networks = await Network.find().select('_id name');

  const extraFilters: {
    [key: string]: {
      type: FilterType;
      relatives: RelativeType[];
      options?: { key: string; value: string }[];
    };
  } = {
    IPv4: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    IPv6: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    networks: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options:
        networks.map((network) => ({
          key: network.name,
          value: network._id.toString(),
        })) || [],
    },
  };

  // Get the base filters
  return await ScannedHost.createFilter(extraFilters, skippedFilter);
};

export const processMacFinding = async (
  data: ScanResultsModel,
  reportedBy: string
): Promise<void> => {
  try {
    // Validate MAC Address Type
    if (!MacType.includes(data.MacType)) {
      Logger.error(
        `Invalid MacType detected: ${data.MacType} for MAC: ${data.MAC} IPv4: ${data.IPv4.join(', ')} IPv6: ${data.IPv6.join(', ')} Reported by: ${reportedBy}`
      );
      return;
    }

    // Getting Existing Scanned Host from previous scan
    const existingScannedHost = await ScannedHost.findOne({ mac: data.MAC });

    // Looking for hostId from MAC and IPv4
    const hostId = await getHostIdFromMac(data.MAC, data.IPv4);

    let hostName;
    if (hostId) {
      hostName = (await RportClient.findOne({ rportId: hostId }, 'name'))?.name;
    }

    // Getting the new status for the scanned host
    let currentStatus: ScannedHostStatus = hostId ? MANAGED_HOST : NON_MANAGED_HOST;

    // If the scanned host is non-manageable and ne host id is found, keep the status as non-manageable
    if (existingScannedHost && existingScannedHost.status === NON_MANAGEABLE_HOST && !hostId) {
      currentStatus = NON_MANAGEABLE_HOST;
    }

    // Audit events
    const auditEvents: ScannedHostAuditEvent[] = [...(existingScannedHost?.audit || [])];

    // Last seen and first seen objects
    const lastSeen = { date: new Date(), seenBy: reportedBy };

    // Array of network IDs detected from the scan
    const networkIds: Types.ObjectId[] = await getNetworksFromScan(data.Interfaces);

    // Building the new status for the scanned host if it has been seen before
    if (existingScannedHost) {
      // Check if status has changed
      const statusChanged = existingScannedHost.status !== currentStatus;

      // Push the audit event if the status has changed
      if (statusChanged) {
        auditEvents.push({ date: new Date(), status: currentStatus });
      }

      // For existing hosts, if mac vendor is not set, look it up
      let macVendor = existingScannedHost.MacVendor;
      if (!macVendor || macVendor.trim() === '') {
        try {
          const macVendorResponse = await lookupMacAddress(data.MAC);
          macVendor = macVendorResponse?.company || '';
        } catch {}
      }

      // Update the existing scanned host
      await ScannedHost.updateOne(
        { mac: data.MAC },
        {
          IPv4: data.IPv4,
          IPv6: data.IPv6,
          MacType: data.MacType,
          MacVendor: macVendor,
          status: currentStatus,
          hostId: hostId || null,
          networks: networkIds,
          lastSeen,
          hostName: hostName,
          audit: auditEvents,
        }
      );
    } else {
      // Add the status to the audit events
      auditEvents.push({ date: new Date(), status: currentStatus });

      let macVendor: string;
      try {
        // Look up MAC vendor
        const macVendorResponse = await lookupMacAddress(data.MAC);

        // Extracting the organization name from the response
        macVendor = macVendorResponse?.company || '';
      } catch {
        macVendor = '';
      }

      // Creating the scanned host
      await ScannedHost.create({
        mac: data.MAC,
        IPv4: data.IPv4,
        IPv6: data.IPv6,
        MacType: data.MacType,
        MacVendor: macVendor,
        status: currentStatus,
        hostId: hostId || null,
        networks: networkIds,
        lastSeen,
        firstSeen: lastSeen,
        hostName: hostName,
        audit: auditEvents,
      });
    }
  } catch (err) {
    Logger.error(
      `Error processing Scanned Host Finding. Detected MAC: ${data.MAC} IPv4: ${data.IPv4} Reported by: ${reportedBy}`,
      err
    );
  }
};

export const getHostIdFromMac = async (
  mac: string,
  ipv4: string[] = []
): Promise<string | null> => {
  try {
    // Check if the MAC address and IPv4 addresses are contained in one of the existing records
    const existingMacAddress = await RportMacAddress.findOne({ mac, ipv4: { $all: ipv4 } });

    return existingMacAddress?.rportId || null;
  } catch (err) {
    throw err;
  }
};

export const calculateScannedHostMetrics = async (
  networkIds: Types.ObjectId[] | string[] = []
): Promise<ScannedHostMetrics> => {
  try {
    // Ensure all IDs are ObjectId instances
    const parsedNetworkIds: Types.ObjectId[] = networkIds.map((id) =>
      id instanceof Types.ObjectId ? id : new Types.ObjectId(id)
    );

    // Base aggregation pipeline
    const pipeline: any[] = [];

    // Add network filter if networkId is provided
    if (parsedNetworkIds.length > 0) {
      pipeline.push({
        $match: { networks: { $in: parsedNetworkIds } },
      });
    }
    // Add group stage to count hosts by status
    pipeline.push({ $group: { _id: '$status', count: { $sum: 1 } } });

    // Execute the aggregation with the pipeline
    const scannedHostDistribution = await ScannedHost.aggregate(pipeline);

    // Initialize formatted object with default values
    const formattedScannedHostDistribution: ScannedHostMetrics = {
      managed: 0,
      non_managed: 0,
      non_manageable: 0,
    };

    // Format aggregation result into the desired structure
    scannedHostDistribution.forEach((item) => {
      formattedScannedHostDistribution[item._id as keyof ScannedHostMetrics] = item.count;
    });

    return formattedScannedHostDistribution;
  } catch (error) {
    Logger.error('Error calculating scanned host metrics:', error);
    throw error;
  }
};

export const deleteOldScannedHosts = async () => {
  // Date threshold for deletion (45 days ago)
  const dateThreshold = new Date();
  dateThreshold.setDate(dateThreshold.getDate() - 45);

  // Find all scanned hosts that has not been seen for more than 45 days
  const oldScannedHosts = await ScannedHost.distinct('_id', {
    'lastSeen.date': { $lt: dateThreshold },
    status: { $ne: MANAGED_HOST }, // Exclude managed hosts
  });

  // If no old scanned hosts found, return early
  if (oldScannedHosts.length === 0) {
    return 'No old scanned hosts found for deletion.';
  }

  // Delete the old scanned hosts
  const result = await ScannedHost.deleteMany({ _id: { $in: oldScannedHosts } });

  return `Deleted ${result.deletedCount} old scanned hosts.`;
};

export const setNonManagedStatusToDeletedHost = async (rportId: string) => {
  // Find the scanned host by rportId
  const scannedHost = await ScannedHost.findOne({ hostId: rportId });

  if (!scannedHost) {
    Logger.info(`Scanned host with hostId ${rportId} not found.`);
    return;
  }

  // Update audit events to mark the host as non managed
  const auditEvents: ScannedHostAuditEvent[] = [
    ...(scannedHost.audit || []),
    { date: new Date(), status: NON_MANAGED_HOST },
  ];

  // Set ScannedHost status to Non Managed
  await ScannedHost.findByIdAndUpdate(scannedHost._id, {
    status: NON_MANAGED_HOST,
    audit: auditEvents,
  });
};
