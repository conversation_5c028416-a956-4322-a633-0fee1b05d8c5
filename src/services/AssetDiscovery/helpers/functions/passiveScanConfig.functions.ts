import { Types } from 'mongoose';

import { Service } from '@shared/schemas';
import { PassiveScanConfig } from '../../schemas/passiveScanConfig.schema';

import { Logger } from '@shared/helpers/classes/logger.class';
import { ShellType } from '@shared/models';

import { getHostIdsFromGroups } from '@services/Rport/helpers/functions/group.functions';
import { getHostByShellType } from '@services/Rport/helpers/functions';
import prescripts from '@services/Rport/helpers/prescripts';

import { executeCommandOnClientsOrGroups } from '@services/Rport/helpers/connections/rport';

import { delay } from '@shared/utils/delay';
import { getBatches } from '@shared/utils/batch';

import { API_BASE_URL, CDN_BASE_URL, HOST_CLIENT_TOKEN, SOAR_ID } from '@shared/constants/env';

export const startAutomaticPassiveScanProcess = async (): Promise<string> => {
  try {
    // Check if the service is enabled
    const serviceEnabled = await Service.findOne({
      internalName: 'asset-discovery',
      enabled: true,
    });
    if (!serviceEnabled) {
      return 'Passive scan service is not enabled';
    }

    // Get the scan config instance
    const passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
    if (!passiveScanConfig) {
      return 'Passive scan config not found';
    }

    if (!passiveScanConfig.enabled) {
      return 'Passive scan config is disabled';
    }

    // check if the scan config is locked (if it is locked, return)
    if (passiveScanConfig.lockStatus) {
      return 'Passive scan config is locked';
    }

    // check the next scan time (if it is not time to scan, return)
    if (passiveScanConfig.nextExecution > new Date()) {
      return 'The next scan time has not been reached yet';
    }

    const { _id, batchSize, interval, groups } = passiveScanConfig;

    await startPassiveScanProcess(_id, batchSize, interval, groups);

    return 'Automatic Passive Scan Process Done';
  } catch (err) {
    throw err;
  }
};

// Function to run a passive scan on the targets
export const startPassiveScanProcess = async (
  configId: Types.ObjectId,
  batchSize: number,
  interval: number,
  groups: Types.ObjectId[]
): Promise<void> => {
  try {
    // Update the lock status to true
    await lockPassiveScanConfig(configId);

    // Get the hosts from the selected group
    const hostIds = await getHostIdsFromGroups(groups);

    if (hostIds.length === 0) {
      Logger.info('No hosts found for passive scan');
      return;
    }

    // Split host in batches based on batchSize from config
    const hostBatches = getBatches(hostIds, batchSize);

    // Run the passive scan on the hosts
    for (const hostBatch of hostBatches) {
      // Check for lock status (if it is locked, return because the scan has been stopped)
      const lockStatus = await checkLockStatus();

      if (!lockStatus) {
        Logger.info('Passive scan config has been unlocked, stopping the scan');
        return;
      }

      // Run the passive scan script on the hosts
      Logger.info(`Trying to run passive scan on ${hostBatch.length} hosts...`);
      await runPassiveScan(hostBatch);

      Logger.info(`Passive scan completed on batch of hosts, waiting for 10 seconds...`);

      // Wait a delay of 10 seconds before running the next batch
      await delay(10000);
    }

    // Unlock and update the next scan time
  } catch (error) {
    Logger.error('Error during passive scan:', error);
  } finally {
    await unlockAndUpdateScanConfigExecution(configId, interval);
  }
};

const lockPassiveScanConfig = async (configId: Types.ObjectId): Promise<void> => {
  // Update the lock status to true
  await PassiveScanConfig.findByIdAndUpdate(configId, { lockStatus: true });

  Logger.info('Passive scan config locked for execution');
};

const unlockAndUpdateScanConfigExecution = async (
  configId: Types.ObjectId,
  interval: number
): Promise<void> => {
  // Unlock and update the next scan time
  await PassiveScanConfig.findByIdAndUpdate(configId, {
    lockStatus: false,
    nextExecution: new Date(Date.now() + interval * 1000),
    lastExecution: new Date(),
  });

  Logger.info('Passive scan config unlocked and next scan time updated');
};

export const checkLockStatus = async (): Promise<boolean> => {
  // Get the scan config instance
  const passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
  if (!passiveScanConfig) {
    Logger.error('Passive scan config not found');
    return false;
  }
  // Check the lock status
  return passiveScanConfig.lockStatus;
};

export const unlockPassiveScanConfig = async (): Promise<void> => {
  // Get the scan config instance
  const passiveScanConfig = await PassiveScanConfig.getPassiveScanConfig();
  if (!passiveScanConfig) {
    Logger.error('Passive scan config not found');
    return;
  }

  // Unlock the passive scan config
  await PassiveScanConfig.findByIdAndUpdate(passiveScanConfig._id, { lockStatus: false });

  Logger.info('Passive scan config unlocked');
};

export const runPassiveScan = async (hosts: string[]): Promise<void> => {
  const validShells: ShellType[] = ['powershell']; // Missing 'bash'
  try {
    // Split the hosts batch based on shell type and run the passive scan on the active ones using the shellType
    await Promise.all(
      validShells.map(async (shellType) => {
        // Run the passive scan script on the active hosts
        await runPassiveScanOnHosts(hosts, shellType);
      })
    );
  } catch (err) {
    throw err;
  }
};

export const runPassiveScanOnHosts = async (
  hosts: string[],
  shellType: ShellType
): Promise<void> => {
  try {
    const hostsByShellType = await getHostByShellType(shellType, hosts);
    const passiveScanScript = await getPassiveScanScript(shellType);

    if (hostsByShellType.connectedHosts.length > 0) {
      // Run the passive scan script on the active hosts
      await runPassiveScanScriptOnHosts(
        hostsByShellType.connectedHosts,
        passiveScanScript,
        shellType
      );
    }
  } catch (error) {
    throw error;
  }
};

const runPassiveScanScriptOnHosts = async (
  hosts: string[],
  passiveScanScript: string,
  shellType: ShellType
): Promise<void> => {
  const cwd = shellType === 'powershell' ? 'C:\\Windows\\Temp' : '/tmp';

  try {
    await executeCommandOnClientsOrGroups(hosts, [], passiveScanScript, 300, shellType, cwd);
  } catch (error) {
    throw error;
  }
};

const getPassiveScanScript = async (shellType: ShellType): Promise<string> => {
  // Select the passive scan script based on the ShellType
  const passiveScanCommands: { [key in ShellType]: () => string } = {
    powershell: () => getWindowsAssetDiscoveryCommand(),
    cmd: () => getWindowsAssetDiscoveryCommand(),
    bash: () => getLinuxAssetDiscoveryCommand(), // Missing 'getLinuxAssetDiscoveryCommand'
  };

  return passiveScanCommands[shellType]();
};

const getWindowsAssetDiscoveryCommand = (): string => {
  let windowsAssetDiscovery = `$tempfile = "$(Get-Date -Format FileDateTime)-Passive-Network-Scan.ps1"; Invoke-WebRequest -Uri "${CDN_BASE_URL}/assetDiscovery/windows/Passive-Network-Scan.ps1" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -gw "${API_BASE_URL}/gw/host" -ct "${HOST_CLIENT_TOKEN}" -s "${SOAR_ID}" ; Remove-Item $tempfile 2>&1 > $null`;

  if (prescripts.windows) {
    windowsAssetDiscovery = prescripts.windows.addPrescripts(windowsAssetDiscovery);
  }

  return windowsAssetDiscovery;
};

const getLinuxAssetDiscoveryCommand = (): string => {
  // let linuxAssetDiscovery = `$tempfile = "$(Get-Date -Format FileDateTime)-get-inventory.ps1"; Invoke-WebRequest -Uri "${CDN_BASE_URL}/inventory/windows/Get-Inventory.ps1" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -gw "${API_BASE_URL}/gw/host" -ct "${HOST_CLIENT_TOKEN}"; Remove-Item $tempfile 2>&1 > $null`;

  // if (prescripts.linux) {
  //   linuxAssetDiscovery = prescripts.linux.addPrescripts(linuxAssetDiscovery);
  // }

  return 'date +%Y-%m-%d_%H:%M:%S;';
  // return linuxAssetDiscovery;
};
