import { Types } from 'mongoose';
import { SchemaBasicModel } from '@shared/models';

export const MacType = ['Universally Administered', 'Locally Administered'] as const;
export type MacType = (typeof MacType)[number];

export const MANAGED_HOST = 'managed';
export const NON_MANAGED_HOST = 'non_managed';
export const NON_MANAGEABLE_HOST = 'non_manageable';

export type ScannedHostStatus = 'managed' | 'non_managed' | 'non_manageable';

export const ManagedStatus = [MANAGED_HOST, NON_MANAGED_HOST, NON_MANAGEABLE_HOST] as const;
export type ManagedStatus = (typeof ManagedStatus)[number];

export interface NetworkInterface {
  name: string;
  cidr: string;
}

export interface ScanResultsModel {
  MAC: string;
  IPv4: string[];
  IPv6: string[];
  MacType: MacType;
  Interfaces: NetworkInterface[];
}

export interface AssetDiscoveryMessage {
  hostId: string; // Host ID that reported the scan
  outputBatch: ScanResultsModel[];
  batchIndex: number;
  totalBatches: number;
}

export interface ScannedHostBaseModel extends SchemaBasicModel {
  mac: string;
  IPv4: string[];
  IPv6: string[];
  MacType: MacType;
  MacVendor: string; // Vendor of the MAC address
  status: ManagedStatus;
  networks: Types.ObjectId[]; // Networks this host belongs to
  firstSeen: {
    date: Date; // Date in which the host was first detected
    seenBy: string; // Host ID that detected the host
  };
  lastSeen: {
    date: Date; // Date in which the host was last detected
    seenBy: string; // Host ID that detected the host
  };
  audit: ScannedHostAuditEvent[]; // Audit events for this host
}

export interface UnmanagedScannedHostModel extends ScannedHostBaseModel {
  hostId?: string;
}

export interface ManagedScannedHostModel extends ScannedHostBaseModel {
  hostId: string;
  hostName: string;
}

export type ScannedHostModel = ManagedScannedHostModel | UnmanagedScannedHostModel;

// Audit event for a Scanned Host Status change
export interface ScannedHostAuditEvent {
  date: Date; // Date in which the status was changed
  status: ScannedHostStatus; // Status of the host
  updatedBy?: string; // User that changed the status
}
