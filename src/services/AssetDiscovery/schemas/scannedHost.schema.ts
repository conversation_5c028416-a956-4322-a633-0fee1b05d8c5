import { Document, Schema, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { MacType, ManagedStatus, ScannedHostModel } from '../helpers/models';

// Sub-schema for seen information (used in firstSeen and lastSeen)
const seenSchema = new Schema(
  { date: { type: Date, required: true }, seenBy: { type: String, required: true } },
  { _id: false }
);

// Sub-schema for audit events
const auditEventSchema = new Schema(
  {
    date: { type: Date, required: true },
    status: { type: String, enum: ManagedStatus, required: true },
    updatedBy: { type: String },
  },
  { _id: false }
);

const scannedHostSchema = schemaFactory({
  mac: { type: String, required: true },
  IPv4: { type: [String], required: true },
  IPv6: { type: [String], required: true },
  MacType: { type: String, enum: MacType, required: true },
  MacVendor: { type: String, required: false, default: '' },
  status: { type: String, enum: ManagedStatus, required: true },
  hostId: { type: String },
  networks: { type: [Types.ObjectId], ref: 'Network', default: [] },
  firstSeen: { type: seenSchema, required: true },
  lastSeen: { type: seenSchema, required: true },
  hostName: { type: String },
  audit: { type: [auditEventSchema], required: true, default: [] },
});

scannedHostSchema.index({ mac: 1 });

export type ScannedHostDocument = ScannedHostModel & Document & { _id: Types.ObjectId };
export const ScannedHost = modelFactory<ScannedHostModel>('ScannedHost', scannedHostSchema);
