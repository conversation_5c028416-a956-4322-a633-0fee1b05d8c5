import { Types } from 'mongoose';

import LANGUAGES from '@root/shared/constants/languages';
import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { AiAssetModel } from '../helpers/models';

const contentSchema = schemaFactory(
  {
    lang: { type: String, enum: Object.values(LANGUAGES), required: true },
    value: { type: String, required: true },
  },
  { _id: false }
);

const aiAssetSchema = schemaFactory({
  assetId: { type: String, required: true },
  content: { type: [contentSchema], required: true },
  createdAt: { type: Date, default: Date.now, index: { expires: '1d' } },
});

aiAssetSchema.index({ assetId: 1 });

export type AiAssetDocument = AiAssetModel & Document & { _id: Types.ObjectId };
export const AiAsset = modelFactory<AiAssetModel>('AiAsset', aiAssetSchema);
