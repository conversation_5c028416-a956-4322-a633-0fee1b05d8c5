import { Request<PERSON>and<PERSON> } from 'express';

import catchAsync from '@root/shared/utils/catch-async';
import { ServiceResponse } from '@root/shared/models/service-response';
import { exportFileOf } from '@root/services/Export/controllers';
import { GetAllQuery } from '@root/shared/types';
import { errors } from '@root/shared/utils/app-errors';

import {
  getAllPolicies,
  getFiltersForPolicies,
  executeAutomoxPolicyOnHost,
  getPolicyById,
} from '../helpers/functions';

export const getPolicies: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit = 100, offset = 0, sort } = req.query as GetAllQuery;

  const result = await getAllPolicies(filter, limit, offset, sort);

  return ServiceResponse.get(result).send(res);
});

export const getPolicy: RequestHandler = catchAsync(async (req, res) => {
  const { policyId } = req.params;

  const result = await getPolicyById(policyId);

  if (!result) throw errors.not_found(`Policy`);

  return ServiceResponse.get({ ...result }).send(res);
});

export const getPoliciesFilters: RequestHandler = catchAsync(async (_, res) => {
  const { filter, fields } = getFiltersForPolicies();

  return ServiceResponse.get({ filter, fields }).send(res);
});

export const exportPolicies: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, getAllPolicies, ({ data }) => data);
});

export const executePolicyOnHost: RequestHandler = catchAsync(async (req, res) => {
  const { policyId, hostId } = req.params;

  const result = await executeAutomoxPolicyOnHost(policyId, hostId);
  return ServiceResponse.post({ result }).send(res);
});
