import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';

import catchAsync from '@root/shared/utils/catch-async';

import { patchableHosts, patchableHostById, scanHost } from '../helpers/functions/host.functions';
import { ServiceResponse } from '@root/shared/models/service-response';
import { GetAllQuery } from '@root/shared/types';
import { exportFileOf } from '@root/services/Export/controllers';
import { availablePatchesByHost, getAllPolicies } from '../helpers/functions';
import {
  AUTOMOX_DEFAULT_LIMIT,
  AUTOMOX_DEFAULT_PAGE,
  AUTOMOX_MAX_LIMIT_PER_PAGE,
} from '../helpers/constants';

const _getAllPatchableHosts = async (
  filter: string = '',
  limit: number | undefined = AUTOMOX_DEFAULT_LIMIT,
  offset: number = 0,
  sort: string = ''
) => {
  const isExport = limit === -1;
  const currentLimit =
    limit && limit > 0 && limit <= AUTOMOX_MAX_LIMIT_PER_PAGE ? limit : AUTOMOX_MAX_LIMIT_PER_PAGE;
  const currentPage = limit && limit > 0 ? Math.floor(offset / currentLimit) : AUTOMOX_DEFAULT_PAGE;

  let allResults: any[] = [];
  let totalSize = 0;
  let fetched = 0;
  let needGetMore = true;

  while (needGetMore) {
    const { size, results } = await patchableHosts(filter, sort, currentLimit, currentPage);

    if (!totalSize) totalSize = size;

    fetched += results.length;
    allResults = allResults.concat(results);

    // Stop if no results are returned or the number of results is less than the current limit,
    if (results.length === 0 || results.length < currentLimit) {
      needGetMore = false;
    }

    // Stop if the total size is less than or equal to fetched results.
    if (totalSize <= fetched) {
      needGetMore = false;
    }

    // Stop if we are not exporting and have fetched enough results.
    if (!isExport && limit <= fetched) {
      needGetMore = false;
    }
  }

  return { size: totalSize, results: allResults };
};

export const getPatchableHosts: RequestHandler = catchAsync(async (req, res) => {
  const { filter, sort, limit = AUTOMOX_DEFAULT_LIMIT, offset = 0 } = req.query as GetAllQuery;

  const { size, results } = await _getAllPatchableHosts(filter, limit, offset, sort);

  return ServiceResponse.get(results, size, +(offset ?? 0)).send(res);
});

export const getPatchableHostById: RequestHandler = catchAsync(async (req, res) => {
  const { id } = req.params;

  const result = await patchableHostById(id);

  return ServiceResponse.get(result).send(res);
});

export const scanPatchableHost: RequestHandler = catchAsync(async (req, res) => {
  const { id } = req.params;

  await scanHost(id);

  return ServiceResponse.get({ message: 'Scan initiated successfully' }).send(res);
});

export const patchableHostsFilters: RequestHandler = catchAsync(async (req, res) => {
  const { data: policies } = await getAllPolicies('', -1, 0, '');

  const filter = {
    name: {
      type: 'STRING',
      relatives: ['is'],
    },
    compliant: {
      type: 'BOOLEAN',
      relatives: ['is'],
    },
    needsReboot: {
      type: 'BOOLEAN',
      relatives: ['is'],
    },
    disconnectedThirtyDays: {
      type: 'BOOLEAN',
      relatives: ['is'],
    },
    connected: {
      type: 'BOOLEAN',
      relatives: ['is'],
    },
    os_family: {
      type: 'STRING',
      relatives: ['is'],
      options: [
        {
          key: 'Linux',
          value: 'Linux',
        },
        {
          key: 'Windows',
          value: 'Windows',
        },
        {
          key: 'Mac',
          value: 'Mac',
        },
      ],
    },
    policy: {
      type: 'STRING',
      relatives: ['is'],
      options: policies.map((policy) => ({
        key: policy.id,
        value: policy.name,
      })),
    },
  };

  const fields = ['display_name', 'os_family', 'patches', 'pending_patches'];

  return ServiceResponse.get({ filter, fields }).send(res);
});

export const exportPatchableHosts: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, _getAllPatchableHosts, ({ results }) => results);
});

const _getAllAvailablePatchesByHost = async (
  hostId: string,
  limit: number | undefined = AUTOMOX_DEFAULT_LIMIT,
  offset: number = 0
) => {
  const currentLimit =
    limit && limit > 0 && limit <= AUTOMOX_MAX_LIMIT_PER_PAGE ? limit : AUTOMOX_MAX_LIMIT_PER_PAGE;
  const currentPage = limit && limit > 0 ? Math.floor(offset / currentLimit) : AUTOMOX_DEFAULT_PAGE;

  let allResults: any[] = [];
  let fetched = 0;
  let needGetMore = true;

  while (needGetMore) {
    const results = await availablePatchesByHost(hostId, currentLimit, currentPage);

    fetched += results.length;
    allResults = allResults.concat(results);

    // Stop if no results are returned or the number of results is less than the current limit,
    if (results.length === 0 || results.length < currentLimit) {
      needGetMore = false;
    }

    // Stop if we are not exporting and have fetched enough results.
    if (limit <= fetched) {
      needGetMore = false;
    }
  }

  return { size: fetched, results: allResults };
};

export const getPackagesByPatchableHostId: RequestHandler = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { limit = AUTOMOX_DEFAULT_LIMIT, offset = 0 } = req.query as GetAllQuery;

  const { size, results } = await _getAllAvailablePatchesByHost(id, limit, offset);

  return ServiceResponse.get(results, size, +(offset ?? 0)).send(res);
});
