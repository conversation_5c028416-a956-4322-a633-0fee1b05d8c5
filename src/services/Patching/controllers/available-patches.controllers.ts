import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';

import catchAsync from '@root/shared/utils/catch-async';

import { availablePatches } from '../helpers/functions/software.functions';
import { GetAllQuery } from '@root/shared/types';
import {
  AUTOMOX_DEFAULT_LIMIT,
  AUTOMOX_DEFAULT_PAGE,
  AUTOMOX_MAX_LIMIT_PER_PAGE,
} from '../helpers/constants';
import { ServiceResponse } from '@root/shared/models/service-response';
import { exportFileOf } from '@root/services/Export/controllers';

const _getAllAvailablePatches = async (
  filter: string = '',
  limit: number | undefined = AUTOMOX_DEFAULT_LIMIT,
  offset: number = 0,
  sort: string = ''
) => {
  const isExport = limit === -1;
  const currentLimit =
    limit && limit > 0 && limit <= AUTOMOX_MAX_LIMIT_PER_PAGE ? limit : AUTOMOX_MAX_LIMIT_PER_PAGE;
  let currentPage = limit && limit > 0 ? Math.floor(offset / currentLimit) : AUTOMOX_DEFAULT_PAGE;

  let allResults: any[] = [];
  let fetched = 0;
  let needGetMore = true;

  while (needGetMore) {
    const { results } = await availablePatches(filter, sort, currentLimit, currentPage);

    fetched += results.length;
    allResults = allResults.concat(results);

    // Stop if no results are returned or the number of results is less than the current limit,
    if (results.length === 0 || results.length < currentLimit) {
      needGetMore = false;
    }

    // Stop if we are not exporting and have fetched enough results.
    if (!isExport && limit <= fetched) {
      needGetMore = false;
    }

    currentPage++;
  }

  return { size: fetched, results: allResults };
};

export const getAvailablePatches: RequestHandler = catchAsync(async (req, res) => {
  const { filter, sort, limit = AUTOMOX_DEFAULT_LIMIT, offset = 0 } = req.query as GetAllQuery;

  const { results, size } = await _getAllAvailablePatches(filter, limit, offset, sort);

  return ServiceResponse.get(results, size, +(offset ?? 0)).send(res);
});

export const availablePatchesFilters: RequestHandler = catchAsync(async (req, res) => {
  const filter = {
    name: {
      type: 'STRING',
      relatives: ['is'],
    },
    display_name: {
      type: 'STRING',
      relatives: ['is'],
    },
    severity: {
      type: 'STRING',
      relatives: ['is'],
      options: [
        {
          key: 'critical',
          value: 'Critical',
        },
        {
          key: 'medium',
          value: 'Medium',
        },
        {
          key: 'no_known_cves',
          value: 'No Known CVEs',
        },
        {
          key: 'unknown',
          value: 'Unknown',
        },
        {
          key: 'none',
          value: 'None',
        },
        {
          key: 'low',
          value: 'Low',
        },
        {
          key: 'high',
          value: 'High',
        },
      ],
    },
    os_family: {
      type: 'STRING',
      relatives: ['is'],
      options: [
        {
          key: 'Linux',
          value: 'Linux',
        },
        {
          key: 'Windows',
          value: 'Windows',
        },
        {
          key: 'Mac',
          value: 'Mac',
        },
      ],
    },
  };

  const fields = ['display-name', 'severity', 'date'];

  return ServiceResponse.get({ filter, fields }).send(res);
});

export const exportAvailablePatches: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, _getAllAvailablePatches, ({ results }) => results);
});
