import { Router } from 'express';
import { param, query } from 'express-validator';

import { hasPermission, protect } from '@shared/middleware/auth-middleware';
import { validateBody } from '@root/shared/middleware/validation-middleware';

import {
  exportPolicies,
  getPolicies,
  getPolicy,
  getPoliciesFilters,
  executePolicyOnHost,
} from '../controllers';

const router = Router();

// Require authenticated user in all requests
router.use(protect);

// Routes
router.get(
  '/',
  hasPermission('read:patching.policies'),
  [
    query('limit').isFloat({ min: 1, max: 500 }).optional({ checkFalsy: false }),
    query('page').isFloat({ min: 0 }).optional({ checkFalsy: false }),
    query('filter').optional({ checkFalsy: false }).isString(),
    query('sort').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  getPolicies
);

router.get('/filters', hasPermission('read:patching.policies'), getPoliciesFilters);

router.post('/export', hasPermission('read:patching.policies'), exportPolicies);

router.get(
  '/:policyId',
  hasPermission('read:patching.policies'),
  [param('policyId').isString()],
  getPolicy
);

router.post(
  '/:policyId/host/:hostId/execute',
  [param('policyId').isString(), param('hostId').isString()],
  hasPermission('execute:patching.policies'),
  executePolicyOnHost
);

export default router;
