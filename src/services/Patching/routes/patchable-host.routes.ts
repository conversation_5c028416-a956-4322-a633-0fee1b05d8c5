import { Router } from 'express';
import { query, param } from 'express-validator';

import { hasPermission, protect } from '@shared/middleware/auth-middleware';
import { validateBody } from '@root/shared/middleware/validation-middleware';

import {
  exportPatchableHosts,
  getPackagesByPatchableHostId,
  getPatchableHostById,
  getPatchableHosts,
  patchableHostsFilters,
  scanPatchableHost,
} from '../controllers';

const router = Router();

// Require authenticated user in all requests
router.use(protect);

// Routes
router.get(
  '/',
  hasPermission('read:patching.hosts'),
  [
    query('limit').isFloat({ min: 1 }).optional({ checkFalsy: false }),
    query('offset').isFloat({ min: 0 }).optional({ checkFalsy: false }),
    query('filter').optional({ checkFalsy: false }).isString(),
    query('sort').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  getPatchableHosts
);

router.get('/filters', hasPermission('read:patching.hosts'), patchableHostsFilters);
router.post('/export', hasPermission('read:patching.hosts'), exportPatchableHosts);

router.get(
  '/:id',
  hasPermission('read:patching.hosts'),
  [param('id').isString().notEmpty().withMessage('ID parameter is required')],
  validateBody,
  getPatchableHostById
);

router.get(
  '/:id/packages',
  hasPermission('read:patching.hosts'),
  [
    query('limit').isFloat({ min: 1 }).optional({ checkFalsy: false }),
    query('offset').isFloat({ min: 0 }).optional({ checkFalsy: false }),
  ],
  validateBody,
  getPackagesByPatchableHostId
);

router.post(
  '/:id/scan',
  hasPermission('execute:patching.hosts'),
  [param('id').isString().notEmpty().withMessage('ID parameter is required')],
  validateBody,
  scanPatchableHost
);

export default router;
