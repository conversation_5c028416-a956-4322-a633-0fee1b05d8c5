import { Router } from 'express';
import { query } from 'express-validator';

import { hasPermission, protect } from '@shared/middleware/auth-middleware';
import { validateBody } from '@root/shared/middleware/validation-middleware';

import {
  availablePatchesFilters,
  exportAvailablePatches,
  getAvailablePatches,
} from '../controllers';

const router = Router();

// Require authenticated user in all requests
router.use(protect);

// Routes
router.get(
  '/',
  hasPermission('read:patching.patches'),
  [
    query('limit').isFloat({ min: 1 }).optional({ checkFalsy: false }),
    query('offset').isFloat({ min: 0 }).optional({ checkFalsy: false }),
    query('filter').optional({ checkFalsy: false }).isString(),
    query('sort').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  getAvailablePatches
);

router.get('/filters', hasPermission('read:patching.patches'), availablePatchesFilters);
router.post('/export', hasPermission('read:patching.patches'), exportAvailablePatches);

export default router;
