import { Router } from 'express';

import { validateFunctionality } from '@shared/middleware/service-middleware';

import PatchableHostRouter from './routes/patchable-host.routes';
import AvailablePatchesRouter from './routes/available-patches.routes';
import DashboardRouter from './routes/dashboard.routes';
import PoliciesRouter from './routes/policies.routes';

const router = Router();
router.use('/dashboard', validateFunctionality('patching.dashboard'), DashboardRouter);

router.use('/patchable-host', validateFunctionality('patching.hosts'), PatchableHostRouter);

router.use('/available-patches', validateFunctionality('patching.patches'), AvailablePatchesRouter);

router.use('/policies', validateFunctionality('patching.policies'), PoliciesRouter);

export default router;
