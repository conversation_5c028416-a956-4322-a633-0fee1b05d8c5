export type Stats = {
  availableUpdatesLast7Days: number;
  compliant: number;
  createTimeLast7Days: number;
  disconnectMore30Days: number;
  failedUpdateAttempts: number;
  incompatible: number;
  needReboot: number;
  needsAttention: number;
  notCompatible: number;
  osDistributionLinux: number;
  osDistributionMac: number;
  osDistributionWindows: number;
  pendingPatches: number;
  recentlyInstalledSensorsLast7Days: number;
  recentlyNeedsAttentionDisconnected30Days: number;
  recentlyNeedsAttentionFailedUpdateAttempts: number;
  recentlyNeedsAttentionNeedReboot: number;
  recentlyNeedsAttentionNotCompatible: number;
  total: number;
  troubleshootingDisconnected30PlusDays: number;
  troubleshootingFailedUpdateAttempts: number;
  troubleshootingNeedsAttention: number;
  troubleshootingNeedsReboot: number;
  troubleshootingNotCompatible: number;
};

export type PatchCount = {
  unknown: number;
  low: number;
  medium: number;
  high: number;
  critical: number;
};

export type NeedsAttentionDevice = {
  id: number;
  name: string;
  needsAttention:
    | 'needsReboot'
    | 'failedUpdateAttempts'
    | 'disconnected30PlusDays'
    | 'notCompatible'
    | undefined;
  last_refresh_time: string;
};
