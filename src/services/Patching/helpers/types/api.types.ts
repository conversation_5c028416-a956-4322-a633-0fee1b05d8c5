// Body params

type SeverityLevel = 'no_known_cves' | 'none' | 'unknown' | 'low' | 'medium' | 'high' | 'critical';

type CommandTypeName = 'Reboot' | 'GetOS' | 'InstallUpdate' | 'InstallAllUpdates';

type Action = {
  attribute: string;
  action: 'apply' | 'remove';
  value: string[];
};

export type UpdateServersBatchBody = {
  devices: number[];
  actions: Action[];
};

export type UpdateServerBody = {
  server_group_id: number;
  ip_addrs?: string;
  exception: boolean;
  tags?: string[];
  custom_name?: string;
};

export type CommandBody = {
  command_type_name: CommandTypeName;
  args?: string;
};

export type BatchCommandBody = {
  batch: number[];
  command_type_name: CommandTypeName;
  args?: string;
};

export type BatchBody = {
  batch: number[];
};

export type GroupBatchCommandBody = {
  batch: number[];
};

export type AutomoxOrgInfo = {
  uuid: string;
  device_count: number;
};

// Responses

export type AutomoxDeviceResponse = {
  id: number;
  os_version_id: number;
  server_group_id: number;
  organization_id: number;
  uuid: string;
  name: string;
  instance_id: string;
  refresh_interval: number;
  last_update_time: string;
  last_refresh_time: string;
  uptime: number;
  needs_reboot: boolean;
  timezone: string;
  tags: string[];
  deleted: boolean;
  create_time: string;
  os_version: string;
  os_name: string;
  os_family: string;
  ip_addrs: string[];
  ip_addrs_private: string[];
  patches: number;
  details: {
    CPU: string;
    DISKS: { SIZE: string; TYPE: string }[];
    MODEL: string;
    NICS: {
      CONNECTED: boolean;
      DEVICE: string;
      IPS: string[];
      MAC: string;
      TYPE: string;
      VENDOR: string;
    }[];
    RAM: string;
    SERIAL: string;
    SERVICETAG: string;
    VENDOR: string;
    VERSION: string;
  };
  agent_version: string;
  custom_name: string;
  exception: boolean;
  total_count: number;
  is_compatible: boolean;
  compatibility_checks: {
    low_diskspace: boolean;
    missing_secure_token: boolean;
    app_store_disconnected: boolean;
    missing_powershell: boolean;
    missing_wmi_integrity_check: boolean;
    wsus_disconnected: boolean;
    windows_update_server_disconnected: boolean;
  };
  policy_status: {
    id: number;
    organization_id: number;
    policy_id: number;
    server_id: number;
    policy_name: string;
    policy_type_name: string;
    status: number;
    result: string;
    create_time: string;
  }[];
  last_scan_failed: boolean;
  pending: boolean;
  compliant: boolean;
  display_name: string;
  commands: {
    command_type_name: string;
    args: string;
    exec_time: string;
  }[];
  pending_patches: number;
  connected: boolean;
  last_process_time: string;
  next_patch_time: string;
  notification_count: number;
  reboot_notification_count: number;
  patch_deferral_count: number;
  is_delayed_by_notification: boolean;
  reboot_is_delayed_by_notification: boolean;
  is_delayed_by_user: boolean;
  reboot_is_delayed_by_user: boolean;
  last_disconnect_time: string;
  needs_attention: boolean;
  serial_number: string;
  status: {
    device_status: string;
    agent_status: string;
    policy_status: string;
    policy_statuses: { id: number; compliant: boolean }[];
  };
  last_logged_in_user: string;
};

export type AutomoxDevicesResponse = {
  results: AutomoxDeviceResponse[];
  size: number;
};

export type AutomoxPolicyStatsResponse = {
  compliant: number;
  noncompliant: number;
  organization_id: number;
  pending: number;
  policy_id: number;
  policy_name: string;
  policy_type_name: 'patch' | 'custom' | 'required_software';
};

type WsusConfig = {
  id: number;
  server_group_id: number;
  is_managed: boolean;
  server_url: string;
  created_at: string;
  updated_at: string;
};

export type ServerGroupResponse = {
  id: number;
  organization_id: number;
  name: string;
  refresh_interval: number;
  parent_server_group_id: number;
  ui_color: string;
  notes: string;
  enable_os_auto_update: boolean;
  server_count: number;
  wsus_config: WsusConfig;
  policies: number[];
};

export type UpdateBatchServersResponse = {
  id: string;
  total_jobs: number;
  pending_jobs: number;
  processed_jobs: number;
  progress: number;
  failed_jobs: number;
  created_at: string;
  cancelled_at: string;
  finished_at: string;
};

export type PackageWithDevicesResponse = {
  id: number;
  server_id: number;
  package_id: number;
  software_id: number;
  installed: boolean;
  ignored: boolean;
  deferred_until: string | null;
  name: string;
  display_name: string;
  version: string;
  repo: string;
  group_ignored: boolean;
  group_deferred_until: string | null;
  cves: string[];
  cve_score: string | null;
  severity: SeverityLevel;
  package_version_id: number;
  os_name: string;
  os_version: string;
  os_version_id: number;
  create_time: string;
  requires_reboot: boolean;
  patch_classification_category_id: number | null;
  patch_scope: string | null;
  is_uninstallable: boolean;
  secondary_id: string;
  is_managed: boolean;
  impact: number;
  organization_id: number;
  agent_severity: SeverityLevel | null;
};

export type PackageInfo = {
  package_version_id: number;
  package_id: number;
  cves: Record<string, any>;
  cve_score: number | null;
  severity: SeverityLevel;
  create_time: string;
  name: string;
  display_name: string;
  is_managed: boolean;
  package_version: string;
  os_family_name: string;
  agent_severity: number;
  os_version_id: number;
  patch_installed: number;
  devices_needed: number;
  os_name: string;
  os_version: string;
  requires_reboot: boolean;
  group_ignored: boolean;
};

export type PackageSearchResponse = {
  results: PackageInfo[];
  size: number;
  count_skipped: boolean;
};

export type AutomoxPolicyResponse = {
  id: number;
  uuid: string;
  name: string;
  policy_type_name: string;
  organization_id: number;
  configuration: Record<string, any>;
  schedule_days: number;
  schedule_weeks_of_month: number;
  schedule_months: number;
  schedule_time: string;
  notes: string;
  create_time: string;
  server_groups: number[];
  server_count: number;
  status: 'active' | 'inactive';
  community_worklet_id: number | null;
  community_worklet_uuid: string | null;
  policy_template_id: number | null;
};
