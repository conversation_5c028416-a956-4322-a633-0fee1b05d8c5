export const AUTOMOX_DEVICES_FILTER_FIELDS_BY_TYPE: Record<string, string[]> = {
  booleans: ['compliant', 'needsReboot', 'disconnectedThirtyDays', 'connected'],
  name_synonyms: ['device_name', 'display_name', 'name', 'os_family'],
  strings: ['os_family'],
};

export const AUTOMOX_SOFTWARE_FILTER_FIELDS_BY_TYPE: Record<string, string[]> = {
  booleans: [],
  name_synonyms: ['display-name', 'name'],
  strings: ['severity', 'os_family'],
};
