import axios from 'axios';

import { AutomoxOrgInfo } from '../types';
import { handleAutomoxErrors } from '../utils';
import { AUTOMOX_ORG_ID, AUTOMOX_API_KEY, AUTOMOX_BASE_URL } from '@shared/constants/env';

export class AutomoxService {
  private static instance: AutomoxService;
  private orgInfo: AutomoxOrgInfo | null = null;
  private initPromise: Promise<void> | null = null;

  private constructor() {}

  static getInstance(): AutomoxService {
    if (!AutomoxService.instance) {
      AutomoxService.instance = new AutomoxService();
      AutomoxService.instance.initPromise = AutomoxService.instance.initOrgInfo();
    }
    return AutomoxService.instance;
  }

  private async initOrgInfo(): Promise<void> {
    if (!this.orgInfo) {
      const endpoint = `/orgs/${AUTOMOX_ORG_ID}`;
      const data = await this.get<AutomoxOrgInfo>(endpoint);
      this.orgInfo = {
        uuid: data.uuid,
        device_count: data.device_count,
      };
    }
  }

  async get<T>(endpointUrl: string, queryParams?: Record<string, any>): Promise<T> {
    await this.initPromise;

    const config = {
      headers: {
        Authorization: `Bearer ${AUTOMOX_API_KEY}`,
      },
      params: queryParams,
    };

    try {
      const response = await axios.get<T>(`${AUTOMOX_BASE_URL}${endpointUrl}`, config);
      return response.data;
    } catch (err: any) {
      throw handleAutomoxErrors(err);
    }
  }

  async post<T>(endpointUrl: string, data?: Record<string, any>): Promise<T> {
    await this.initPromise;

    const config = {
      headers: {
        Authorization: `Bearer ${AUTOMOX_API_KEY}`,
      },
    };

    try {
      const response = await axios.post<T>(`${AUTOMOX_BASE_URL}${endpointUrl}`, data, config);
      return response.data;
    } catch (err: any) {
      throw handleAutomoxErrors(err);
    }
  }

  getOrgInfo(): AutomoxOrgInfo | null {
    return this.orgInfo;
  }
}
