import { AxiosError } from 'axios';
import { generateError } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

// This function handles halcyon errors in responses
export const handleAutomoxErrors = (error: AxiosError) => {
  Logger.error('Automox API error:', error);

  if (!error.isAxiosError) {
    throw generateError('Error communicating with Automox API', 500, 'AUTOMOX_ERROR');
  }

  const response = error.response as {
    data: { errors: string[] };
    status?: number;
  };

  if (!response?.data) {
    throw generateError('Error communicating with Automox API', 500, 'AUTOMOX_ERROR');
  }

  if (!response.data.errors) {
    throw generateError('Error communicating with Automox API', 500, 'AUTOMOX_ERROR');
  }

  if (response.status === 401) {
    throw generateError('Unauthorized', 401, 'AUTOMOX_AUTHENTICATION_ERROR');
  }

  const automoxErrors = response.data.errors;

  if (automoxErrors.length > 1) {
    throw generateError(
      automoxErrors.join('\n'),
      response.status || 500,
      'AUTOMOX_MULTIPLE_ERRORS'
    );
  } else if (automoxErrors.length === 1) {
    throw generateError(automoxErrors[0], response.status || 500, 'AUTOMOX_ERROR');
  } else {
    throw generateError('Error communicating with Automox API', 500, 'AUTOMOX_ERROR');
  }
};
