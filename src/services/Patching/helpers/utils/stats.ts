import { AutomoxDeviceResponse, NeedsAttentionDevice, Stats } from '../types';

type FilterFunction = (server: AutomoxDeviceResponse) => boolean;

const filterServers = (
  servers: AutomoxDeviceResponse[],
  filter: FilterFunction
): AutomoxDeviceResponse[] => servers.filter(filter);

const countServersByFilter = (servers: AutomoxDeviceResponse[], filter: FilterFunction): number =>
  filterServers(servers, filter).length;

const getDaysAgo = (days: number): Date =>
  new Date(new Date().getTime() - days * 24 * 60 * 60 * 1000);

const filters: { [key: string]: FilterFunction } = {
  // Compliant devices
  compliant: (server) => server.compliant,

  // Available updates (last 7 days)
  availableUpdatesLast7Days: (server) => {
    const createTime = new Date(server.create_time);
    return createTime >= getDaysAgo(7);
  },

  // Recently installed sensors (last 7 days)
  recentlyInstalledSensorsLast7Days: (server) => {
    const createTime = new Date(server.create_time);
    return createTime >= getDaysAgo(7);
  },

  // Pending Patches
  pendingPatches: (server) => server.pending_patches > 0,

  // Need reboot
  needReboot: (server) => server.needs_reboot,

  // Failed update attempts
  failedUpdateAttempts: (server) =>
    server.status.policy_status.toLowerCase().includes('non-compliant'),

  // Disconnected for 30+ days
  disconnectMore30Days: (server) => {
    const disconnectTime = new Date(server.last_disconnect_time);
    return !server.connected && disconnectTime <= getDaysAgo(30);
  },

  // Not compatible
  notCompatible: (server) => !server.is_compatible,

  // Device troubleshooting - needs reboot
  troubleshootingNeedsReboot: (server) => server.needs_attention && server.needs_reboot,

  // Device troubleshooting - failed update attempts
  troubleshootingFailedUpdateAttempts: (server) =>
    server.needs_attention && server.status.policy_status.toLowerCase().includes('non-compliant'),

  // Device troubleshooting - disconnected for +30 days
  troubleshootingDisconnected30PlusDays: (server) => {
    const disconnectTime = new Date(server.last_disconnect_time);
    return server.needs_attention && !server.connected && disconnectTime <= getDaysAgo(30);
  },

  // Device troubleshooting - not compatible
  troubleshootingNotCompatible: (server) => server.needs_attention && !server.is_compatible,

  // Device troubleshooting - needs attention
  troubleshootingNeedsAttention: (server) => server.needs_attention,

  // OS Distribution - Linux
  osDistributionLinux: (server) => server.os_family.toLowerCase().includes('linux'),

  // OS Distribution - Mac
  osDistributionMac: (server) => server.os_family.toLowerCase().includes('mac'),

  // OS Distribution - Windows
  osDistributionWindows: (server) => server.os_family.toLowerCase().includes('windows'),

  // Recently needs attention - Need reboot
  recentlyNeedsAttentionNeedReboot: (server) => server.needs_attention && server.needs_reboot,

  // Recently needs attention - Failed update attempts
  recentlyNeedsAttentionFailedUpdateAttempts: (server) =>
    server.needs_attention && server.status.policy_status.toLowerCase().includes('non-compliant'),

  // Recently needs attention - Disconnected 30 days
  recentlyNeedsAttentionDisconnected30Days: (server) => {
    const disconnectTime = new Date(server.last_disconnect_time);
    return server.needs_attention && !server.connected && disconnectTime <= getDaysAgo(30);
  },

  // Recently needs attention - Not compatible
  recentlyNeedsAttentionNotCompatible: (server) => server.needs_attention && !server.is_compatible,
};

export const getStats = (servers: AutomoxDeviceResponse[]): Stats => {
  const stats: { [key: string]: number } = {
    total: servers.length,
  };

  for (const key in filters) {
    stats[key] = countServersByFilter(servers, filters[key]);
  }

  return stats as Stats;
};

const getPriorityFilter = (server: AutomoxDeviceResponse) => {
  if (filters.recentlyNeedsAttentionNeedReboot(server)) {
    return 'needsReboot';
  }
  if (filters.recentlyNeedsAttentionFailedUpdateAttempts(server)) {
    return 'failedUpdateAttempts';
  }
  if (filters.recentlyNeedsAttentionDisconnected30Days(server)) {
    return 'disconnected30PlusDays';
  }
  if (filters.recentlyNeedsAttentionNotCompatible(server)) {
    return 'notCompatible';
  }
  return undefined;
};

export const getRecentlyNeedsAttention = (
  servers: AutomoxDeviceResponse[]
): NeedsAttentionDevice[] => {
  const uniqueServerIds = new Set<number>();
  const parsedServers: NeedsAttentionDevice[] = [];

  servers.forEach((server) => {
    const priorityFilter = getPriorityFilter(server);
    if (priorityFilter && !uniqueServerIds.has(server.id)) {
      uniqueServerIds.add(server.id);
      parsedServers.push({
        id: server.id,
        name: server.display_name || server.custom_name || server.name,
        needsAttention: priorityFilter,
        last_refresh_time: server.last_refresh_time,
      });
    }
  });

  parsedServers.sort((a, b) => {
    const dateA: Date = new Date(a.last_refresh_time);
    const dateB: Date = new Date(b.last_refresh_time);

    if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
      return dateB.getTime() - dateA.getTime();
    }

    return 0;
  });

  return parsedServers.slice(0, 30) || [];
};
