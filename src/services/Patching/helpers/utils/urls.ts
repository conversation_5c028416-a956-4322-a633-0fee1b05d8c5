import {
  AUTOMOX_DEVICES_FILTER_FIELDS_BY_TYPE,
  AUTOMOX_SOFTWARE_FILTER_FIELDS_BY_TYPE,
} from '../constants';

export const parseSortParam = (sort: string): { sortColumn: string; sortDir: string } => {
  const [sortColumn, sortDir] = sort.split(':');
  const normalizedSortDir = sortDir === '-1' ? 'desc' : 'asc';

  return { sortColumn, sortDir: normalizedSortDir };
};

export const addDeviceFilters = (
  queryParams: URLSearchParams,
  filter: { [key: string]: { [key: string]: string | string[] } } = {}
) => {
  Object.keys(filter).forEach((field) => {
    if (AUTOMOX_DEVICES_FILTER_FIELDS_BY_TYPE.booleans.includes(field)) {
      const booleanValue = filter[field].value.toString().toLowerCase();
      const value = booleanValue === 'true' ? 1 : 0;
      queryParams.append(field, value.toString());
    } else if (filter[field].hasOwnProperty('values')) {
      const values = filter[field].values as string[];
      values.forEach((value) => {
        queryParams.append(`filters[${field}][]`, value);
      });
    } else if (AUTOMOX_DEVICES_FILTER_FIELDS_BY_TYPE.name_synonyms.includes(field)) {
      queryParams.append(`filters[device_name][]`, `${filter[field].value}`);
    } else if (field === 'policy') {
      queryParams.append(`policyId[]`, `${filter[field].value}`);
    } else {
      queryParams.append(field, `${filter[field].value}`);
    }
  });
};

export const addSoftwareFilters = (
  queryParams: URLSearchParams,
  filter: { [key: string]: { [key: string]: string | string[] } } = {}
) => {
  Object.keys(filter).forEach((field) => {
    if (AUTOMOX_SOFTWARE_FILTER_FIELDS_BY_TYPE.booleans.includes(field)) {
      const booleanValue = filter[field].value.toString().toLowerCase();
      const value = booleanValue === 'true' ? 1 : 0;
      queryParams.append(field, value.toString());
    } else if (filter[field].hasOwnProperty('values')) {
      const values = filter[field].values as string[];
      values.forEach((value) => {
        queryParams.append(`filters[${field}][]`, value);
      });
    } else if (AUTOMOX_SOFTWARE_FILTER_FIELDS_BY_TYPE.name_synonyms.includes(field)) {
      queryParams.append(`filter[]`, `${filter[field].value}`);
    } else {
      queryParams.append(field, `${filter[field].value}`);
    }
  });
};
