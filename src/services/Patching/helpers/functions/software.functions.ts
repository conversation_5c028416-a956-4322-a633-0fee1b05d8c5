import { PackageInfo, PackageSearchResponse } from '../types';
import { AutomoxService } from '../connections/automox.connection';

import { AUTOMOX_ORG_ID } from '@shared/constants/env';
import { AUTOMOX_DEFAULT_LIMIT, AUTOMOX_DEFAULT_PAGE } from '../constants';
import { addSoftwareFilters, parseSortParam } from '../utils';
import { parseFiltersStringToObject } from '@root/shared/utils/filters';

export const availablePatches = async (
  filter?: string,
  sort?: string,
  limit: number = AUTOMOX_DEFAULT_LIMIT,
  page: number = AUTOMOX_DEFAULT_PAGE
): Promise<PackageSearchResponse> => {
  const automoxService = AutomoxService.getInstance();

  const queryParams = new URLSearchParams();
  queryParams.append('o', AUTOMOX_ORG_ID);
  queryParams.append('includeUnmanaged', '0'); // Include applications Automox does not currently support for patching.
  queryParams.append('filters[is_managed]', '1'); // Filter by managed status of the package. 1: Managed packages, 0: Unmanaged packages.
  queryParams.append('awaiting', '1'); // Filter based installation status of package. awaiting=1: Packages that are currently available but not installed. awaiting=0: Packages that are already installed.

  if (filter) {
    const filters = filter ? parseFiltersStringToObject(filter, []) : {};
    addSoftwareFilters(queryParams, filters);
  }

  if (sort) {
    const parsedSort = parseSortParam(sort);
    queryParams.append(`sort`, parsedSort.sortColumn);
    queryParams.append(`dir`, parsedSort.sortDir);
  }

  if (limit) queryParams.append('l', limit.toString());
  if (page) queryParams.append('p', page.toString());

  const endpoint = `/orgs/${AUTOMOX_ORG_ID}/packages/search?${queryParams.toString()}`;

  return automoxService.get<PackageSearchResponse>(endpoint, queryParams);
};

export const availablePatchesByHost = async (
  hostId: string,
  limit: number = AUTOMOX_DEFAULT_LIMIT,
  page: number = AUTOMOX_DEFAULT_PAGE
): Promise<PackageInfo[]> => {
  const automoxService = AutomoxService.getInstance();

  const queryParams = new URLSearchParams();
  queryParams.append('o', AUTOMOX_ORG_ID);
  queryParams.append('internal', '1');

  if (limit) queryParams.append('l', limit.toString());
  if (page) queryParams.append('p', page.toString());

  const endpoint = `/servers/${hostId}/packages?${queryParams.toString()}`;

  return automoxService.get<PackageInfo[]>(endpoint, queryParams);
};
