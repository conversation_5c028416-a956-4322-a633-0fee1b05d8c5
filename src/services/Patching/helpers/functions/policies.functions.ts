import { AUTOMOX_ORG_ID } from '@shared/constants/env';
import { Logger } from '@root/shared/helpers/classes/logger.class';
import { parseFiltersStringToObject } from '@root/shared/utils/filters';
import { useCache } from '@root/shared/utils/cache.utils';

import { AutomoxService } from '../connections/automox.connection';
import { AutomoxPolicyResponse } from '../types';
import { PolicyType } from '../enums';

const POLICIES_CACHE_TTL = 5 * 60; // 5min
const POLICIES_CACHE_KEY = 'automox_policies_all';

const fetchAllPoliciesFromAPI = async (): Promise<AutomoxPolicyResponse[]> => {
  const automoxService = AutomoxService.getInstance();
  const allPolicies: AutomoxPolicyResponse[] = [];

  let page = 0;
  const limit = 500;

  while (true) {
    const queryParams = new URLSearchParams({
      o: AUTOMOX_ORG_ID,
      limit: limit.toString(),
      page: page.toString(),
    });

    const endpoint = '/policies';
    const result = await automoxService.get<AutomoxPolicyResponse[]>(endpoint, queryParams);

    allPolicies.push(...result);
    if (result.length < limit) break;

    page++;
  }

  return allPolicies.map((policy) => ({
    ...policy,
    type: getPolicyType(policy),
    schedule: parseScheduleToNaturalLanguage(policy),
  }));
};

export const fetchAllPolicies = async (
  forceRefresh: boolean = false
): Promise<AutomoxPolicyResponse[]> => {
  const cacheKey = forceRefresh ? `${POLICIES_CACHE_KEY}_${Date.now()}` : POLICIES_CACHE_KEY;

  return await useCache(fetchAllPoliciesFromAPI, cacheKey, POLICIES_CACHE_TTL);
};

export const getAllPolicies = async (
  filter?: string,
  limit: number = 500,
  offset: number = 0,
  sort?: string
): Promise<{
  data: AutomoxPolicyResponse[];
  meta: { count: number; resources: number; offset: number };
}> => {
  // get policies from cache
  let allPolicies = await fetchAllPolicies();

  if (filter) {
    allPolicies = applyFilters(allPolicies, filter);
  }

  const sorted = applySort(allPolicies, sort);

  const paginated = sorted.slice(offset, offset + limit);

  return {
    data: paginated,
    meta: {
      count: sorted.length,
      resources: paginated.length,
      offset,
    },
  };
};

export const getPolicyById = async (
  policyId: string,
  forceRefresh: boolean = false
): Promise<AutomoxPolicyResponse | null> => {
  const allPolicies = await fetchAllPolicies(forceRefresh);
  return allPolicies.find((policy) => policy.id.toString() === policyId) || null;
};

export const getFiltersForPolicies = (): { filter: { [key: string]: any }; fields: string[] } => {
  const filter = {
    name: {
      type: 'STRING',
      relatives: ['is', 'is_not', 'contains', 'not_contains'],
    },
  };

  const fields: string[] = ['name', 'type', 'status', 'server_count'];

  return { filter, fields };
};

export const executeAutomoxPolicyOnHost = async (
  policyId: string,
  hostId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const automoxService = AutomoxService.getInstance();

    const endpoint = `/policies/${policyId}/action?o=${AUTOMOX_ORG_ID}`;

    await automoxService.post(endpoint, {
      serverId: hostId,
      action: 'remediateServer',
    });

    return { success: true, message: 'Policy executed successfully' };
  } catch (error) {
    Logger.error(`Error executing Automox policy: `, error);
    return { success: false, message: `Error executing policy` };
  }
};

export const applyFilters = (
  policies: AutomoxPolicyResponse[],
  filtersString: string
): AutomoxPolicyResponse[] => {
  const parsedFilters = parseFiltersStringToObject(filtersString);
  let filtered = [...policies];

  for (const field in parsedFilters) {
    const { condition, value, values } = parsedFilters[field];
    const val = (value || values?.[0] || '').toLowerCase();

    switch (condition) {
      case 'is':
        filtered = filtered.filter((p) => String((p as any)[field]).toLowerCase() === val);
        break;
      case 'is_not':
        filtered = filtered.filter((p) => String((p as any)[field]).toLowerCase() !== val);
        break;
      case 'contains':
        filtered = filtered.filter((p) =>
          String((p as any)[field])
            .toLowerCase()
            .includes(val)
        );
        break;
      case 'not_contains':
        filtered = filtered.filter(
          (p) =>
            !String((p as any)[field])
              .toLowerCase()
              .includes(val)
        );
        break;
      default:
        break;
    }
  }

  return filtered;
};

const applySort = (data: AutomoxPolicyResponse[], sort?: string): AutomoxPolicyResponse[] => {
  const fallbackSort = [...data].sort((a, b) => {
    return new Date(b.create_time).getTime() - new Date(a.create_time).getTime();
  });

  if (!sort) return fallbackSort;

  const [field, directionRaw] = sort.split(':');
  const direction = parseInt(directionRaw, 10);

  const validFields = ['name', 'type', 'status', 'server_count'];
  if (!validFields.includes(field)) return fallbackSort;
  if (![1, -1].includes(direction)) return fallbackSort;

  return [...data].sort((a, b) => {
    const aVal = field === 'type' ? a.policy_type_name : (a as any)[field];
    const bVal = field === 'type' ? b.policy_type_name : (b as any)[field];

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return aVal.localeCompare(bVal) * direction;
    }

    return (aVal - bVal) * direction;
  });
};

const getPolicyType = (policy: AutomoxPolicyResponse): PolicyType => {
  if (policy.policy_type_name === 'custom') return PolicyType.WORKLET;

  if (policy.policy_type_name === 'patch') {
    const { filter_type, patch_rule } = policy.configuration || {};

    if (filter_type === 'all' && patch_rule === 'all') return PolicyType.PATCH_ALL;
    if (filter_type === 'exclude' && patch_rule === 'filter') return PolicyType.PATCH_ALL_EXCEPT;
    if (filter_type === 'include' && patch_rule === 'filter') return PolicyType.PATCH_ONLY;
    if (filter_type === 'all' && patch_rule === 'manual') return PolicyType.PATCH_MANUAL_APPROVAL;
    if (filter_type === 'severity' && patch_rule === 'filter') return PolicyType.PATCH_BY_SEVERITY;
    if (filter_type === 'all' && patch_rule === 'advanced') return PolicyType.PATCH_ADVANCED;

    return PolicyType.PATCH_ALL;
  }

  return PolicyType.PATCH_ALL;
};

export function parseScheduleToNaturalLanguage(policy: any): string {
  if (
    policy.schedule_days === 0 &&
    policy.schedule_weeks_of_month === 0 &&
    policy.schedule_months === 0
  ) {
    return '';
  }

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  const getActiveDays = (daysBitmask: number): string[] => {
    const days: string[] = [];
    for (let i = 0; i < 7; i++) {
      if (daysBitmask & (1 << i)) {
        days.push(dayNames[i]);
      }
    }
    return days;
  };

  const getActiveWeeks = (weeksBitmask: number): string[] => {
    const weeks: string[] = [];
    const weekNumbers = ['', '1st', '2nd', '3rd', '4th', '5th'];
    for (let i = 1; i <= 5; i++) {
      if (weeksBitmask & (1 << i)) {
        weeks.push(weekNumbers[i]);
      }
    }
    return weeks;
  };

  const getActiveMonths = (monthsBitmask: number): string[] => {
    const months: string[] = [];
    const monthNames = [
      '',
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    for (let i = 1; i <= 12; i++) {
      if (monthsBitmask & (1 << i)) {
        months.push(monthNames[i]);
      }
    }
    return months;
  };

  const activeDays = getActiveDays(policy.schedule_days);
  const activeWeeks = getActiveWeeks(policy.schedule_weeks_of_month);
  const activeMonths = getActiveMonths(policy.schedule_months);

  if (policy.schedule_days === 254) {
    if (policy.schedule_months === 8190) {
      return 'Every day of every month';
    }
    return `Every day of ${activeMonths.join(', ')}`;
  }

  if (policy.schedule_days === 8 && policy.schedule_weeks_of_month === 4) {
    const dayName = activeDays[0];
    const weekName = activeWeeks[0];
    const monthName = activeMonths[0];
    return `${weekName} ${dayName} of ${monthName}`;
  }

  let message = '';

  if (activeDays.length > 0) {
    if (activeDays.length === 1) {
      message = activeDays[0];
    } else if (activeDays.length === 7) {
      message = 'Every day';
    } else {
      message = activeDays.join(', ');
    }
  }

  if (activeWeeks.length > 0 && activeDays.length > 0) {
    if (activeWeeks.length === 1) {
      message = `${activeWeeks[0]} ${message}`;
    } else {
      message = `${activeWeeks.join(', ')} ${message}`;
    }
  }

  if (activeMonths.length > 0) {
    if (activeMonths.length === 12) {
      message += ' of every month';
    } else if (activeMonths.length === 1) {
      message += ` of ${activeMonths[0]}`;
    } else {
      message += ` of ${activeMonths.join(', ')}`;
    }
  }

  return message;
}
