import { AutomoxDevicesResponse } from '../types';
import { AutomoxService } from '../connections/automox.connection';
import { parseSortParam, addDeviceFilters } from '../utils';
import { AutomoxDeviceResponse } from '@root/services/Automox/helpers/types';
import { parseFiltersStringToObject } from '@root/shared/utils/filters';
import { AUTOMOX_DEFAULT_LIMIT, AUTOMOX_DEFAULT_PAGE } from '../constants';

export const patchableHosts = async (
  filter?: string,
  sort?: string,
  limit: number = AUTOMOX_DEFAULT_LIMIT,
  page: number = AUTOMOX_DEFAULT_PAGE
): Promise<AutomoxDevicesResponse> => {
  const automoxService = AutomoxService.getInstance();

  const queryParams = new URLSearchParams();

  if (filter) {
    const filters = filter ? parseFiltersStringToObject(filter, []) : {};
    addDeviceFilters(queryParams, filters);
  }

  if (sort) {
    const parsedSort = parseSortParam(sort);
    queryParams.append(`sortColumns[]`, parsedSort.sortColumn);
    queryParams.append(`sortDir`, parsedSort.sortDir);
  }

  if (limit) queryParams.append('l', limit.toString());
  if (page) queryParams.append('p', page.toString());

  const endpoint = `/servers`;

  return automoxService.get<AutomoxDevicesResponse>(endpoint, queryParams);
};

export const patchableHostById = async (id: string): Promise<AutomoxDeviceResponse> => {
  const automoxService = AutomoxService.getInstance();
  const endpoint = `/servers/${id}`;

  return automoxService.get<AutomoxDeviceResponse>(endpoint);
};

export const scanHost = async (id: string): Promise<void> => {
  const automoxService = AutomoxService.getInstance();
  const endpoint = `/servers/batch/queues`;
  const payload = {
    batch: [id],
    command_type_name: 'GetOS',
  };

  await automoxService.post(endpoint, payload);
};
