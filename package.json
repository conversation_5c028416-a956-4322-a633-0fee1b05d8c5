{"name": "soar", "version": "1.0.0", "description": "", "main": "main.ts", "engines": {"node": ">=18"}, "scripts": {"start": "TS_NODE_PROJECT=tsconfig-paths.json node -r tsconfig-paths/register dist/main.js", "clean": "rm -rf dist/", "build": "tsc --build --pretty", "dev": "ts-node-dev -r tsconfig-paths/register --rs --respawn --transpile-only src/main.ts", "test": "jest --no-cache --runInBand --verbose=true", "migrations": "migrate-mongo", "lint": "eslint \"src/**/*.ts\"", "check:types": "tsc --noEmit", "prepare": "husky"}, "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@date-fns/tz": "^1.2.0", "@elastic/elasticsearch": "^8.18.0", "@langchain/core": "^0.3.61", "@langchain/openai": "^0.5.15", "@maizzle/framework": "^4.8.9", "@supercharge/request-ip": "^1.2.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "canvas": "^2.11.2", "chart.js": "^4.4.9", "compression": "^1.8.1", "cors": "^2.8.5", "croner": "^9.0.0", "csv-parser": "^3.1.0", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "elastic-apm-node": "^4.12.0", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.0.0", "image-size": "^1.2.0", "ip-cidr": "^4.0.2", "json-2-csv": "^5.5.7", "kafkajs": "^2.2.4", "langchain": "^0.3.29", "leaflet": "^1.9.4", "lodash": "^4.17.21", "migrate-mongo": "^11.0.0", "moment": "^2.30.1", "mongoose": "^8.16.1", "morgan": "^1.10.1", "multer": "^2.0.2", "multer-s3": "^3.0.1", "mysql2": "^3.14.1", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "pdfkit": "^0.17.1", "openai": "^5.6.0", "pdfkit-table": "^0.1.99", "semver": "^7.7.1", "telegraf": "^4.16.3", "tsconfig": "^7.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "validator": "^13.12.0"}, "devDependencies": {"@azure/msal-node": "^2.16.2", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@jest-mock/express": "^2.1.0", "@types/bcryptjs": "^2.4.6", "@types/btoa": "^1.2.5", "@types/chart.js": "^2.9.41", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-serve-static-core": "^5.0.4", "@types/helmet": "^4.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.14", "@types/migrate-mongo": "^10.0.5", "@types/mongodb": "^4.0.7", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/multer-s3": "^3.0.3", "@types/node": "^22.10.6", "@types/nodemailer": "^6.4.17", "@types/nodemon": "^1.19.6", "@types/pdfkit": "^0.13.8", "@types/semver": "^7.5.8", "@types/validator": "^13.12.2", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unused-imports": "^4.1.4", "fast-diff": "^1.3.0", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-date-mock": "^1.0.10", "lint-staged": "^15.4.3", "mongodb-memory-server": "^10.1.3", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "ts-node-dev": "^2.0.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}