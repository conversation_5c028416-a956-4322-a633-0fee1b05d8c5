{"data": {"investigatable_investigatable_id": "286fb047e7b6443db41dfc3d2679db2f:ind:e7964c714d4d4c8faa8c912595907318:*************-10305-26128", "investigatable_product_epp_behavior_custom_ioa_rule_instance_id": "", "investigatable_product_epp_behavior_ioa_description": "A medium level detection was triggered on this process for testing purposes.", "investigatable_product_epp_behavior_ioa_name": "TestTriggerMedium", "investigatable_product_epp_behavior_ioc_type": "", "investigatable_product_epp_behavior_ioc_value": "", "investigatable_product_epp_behavior_objective": "Falcon Detection Method", "investigatable_product_epp_behavior_pattern_disposition_id": "Endpoint detection, standard detection", "investigatable_product_epp_behavior_tactic_name": "Falcon Overwatch", "investigatable_product_epp_behavior_technique_name": "Malicious Activity", "investigatable_product_epp_behavior_timestamp": "2025-07-31T19:45:33Z", "investigatable_product_epp_behavior_timestamp_date": "2025-07-31", "investigatable_product_epp_behavior_timestamp_day_of_week": "Thursday", "investigatable_product_epp_behavior_timestamp_hour": "19", "investigatable_product_epp_behavior_timestamp_minute": "45", "investigatable_product_epp_behavior_timestamp_timezone": "UTC", "investigatable_product_epp_behavior_user_sid": "S-1-5-21-1326125336-3025896452-660313131-1000", "investigatable_product_epp_grand_parent_process_command_line": "C:\\Windows\\Explorer.EXE", "investigatable_product_epp_grand_parent_process_file_path": "\\Device\\HarddiskVolume4\\Windows\\explorer.exe", "investigatable_product_epp_grand_parent_process_image_file_name": "explorer.exe", "investigatable_product_epp_grand_parent_process_local_process_id": "6652", "investigatable_product_epp_grand_parent_process_md5": "", "investigatable_product_epp_grand_parent_process_sha256": "1915641ce7e77936d2ab4e727e1fb23bebc0c3a3dde8aef3116c3dc9165a4974", "investigatable_product_epp_grand_parent_process_threat_graph_process_id": "pid:e7964c714d4d4c8faa8c912595907318:1988700778368", "investigatable_product_epp_grand_parent_process_user_name": "<PERSON><PERSON><PERSON><PERSON>", "investigatable_product_epp_last_update": "2025-07-31T19:46:35Z", "investigatable_product_epp_last_update_date": "2025-07-31", "investigatable_product_epp_last_update_day_of_week": "Thursday", "investigatable_product_epp_last_update_hour": "19", "investigatable_product_epp_last_update_minute": "46", "investigatable_product_epp_last_update_timezone": "UTC", "investigatable_product_epp_parent_process_command_line": "\"C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\" ", "investigatable_product_epp_parent_process_file_path": "\\Device\\HarddiskVolume4\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "investigatable_product_epp_parent_process_image_file_name": "powershell.exe", "investigatable_product_epp_parent_process_local_process_id": "6828", "investigatable_product_epp_parent_process_md5": "ef46f77a6aa0b8e734d9d6704fb5e8aa", "investigatable_product_epp_parent_process_sha256": "52e2de6044af90b16ee48e2f12f4b2260b0d6ce4a96e125501c7d7a503ca4f67", "investigatable_product_epp_parent_process_threat_graph_process_id": "pid:e7964c714d4d4c8faa8c912595907318:1988884318976", "investigatable_product_epp_parent_process_user_name": "<PERSON><PERSON><PERSON><PERSON>", "investigatable_product_epp_process_command_line": "\"C:\\Windows\\system32\\cmd.exe\" crowdstrike_test_medium", "investigatable_product_epp_process_file_path": "\\Device\\HarddiskVolume4\\Windows\\System32\\cmd.exe", "investigatable_product_epp_process_image_file_name": "cmd.exe", "investigatable_product_epp_process_local_process_id": "5816", "investigatable_product_epp_process_md5": "16e584daddc4ed662de47a74da31cb3f", "investigatable_product_epp_process_sha256": "b45185c12649e820408dbddbdd2d08aba2243dd34373e9c58c44496c83c42f0f", "investigatable_product_epp_process_threat_graph_process_id": "pid:e7964c714d4d4c8faa8c912595907318:*************", "investigatable_product_epp_process_user_name": "<PERSON><PERSON><PERSON><PERSON>", "investigatable_product_epp_sensor_bios_manufacturer": "Parallels International GmbH.", "investigatable_product_epp_sensor_bios_version": "20.4.0 (55980)", "investigatable_product_epp_sensor_cloud_instance_id": "", "investigatable_product_epp_sensor_cloud_service_provider": "", "investigatable_product_epp_sensor_cloud_service_provider_account_id": "", "investigatable_product_epp_sensor_domain": "", "investigatable_product_epp_sensor_external_i_pv6": "", "investigatable_product_epp_sensor_external_ip": "**************", "investigatable_product_epp_sensor_groups": "[\"87a8460bab4e4731acb85a470ec96de7\"]", "investigatable_product_epp_sensor_hostname": "FEDE-WIN11-VM", "investigatable_product_epp_sensor_local_i_pv6": "", "investigatable_product_epp_sensor_local_ip": "***********", "investigatable_product_epp_sensor_mac_address": "00-1c-42-43-64-f9", "investigatable_product_epp_sensor_os_version": "Windows 11", "investigatable_product_epp_sensor_ou": "", "investigatable_product_epp_sensor_platform": "Windows", "investigatable_product_epp_sensor_product_type": "Workstation", "investigatable_product_epp_sensor_sensor_id": "e7964c714d4d4c8faa8c912595907318", "investigatable_product_epp_sensor_sensor_version": "7.26.19811.0", "investigatable_product_epp_sensor_site_name": "", "investigatable_product_epp_sensor_system_manufacturer": "Parallels International GmbH.", "investigatable_product_epp_sensor_system_product_name": "Parallels ARM Virtual Machine", "investigatable_product_epp_sensor_tags": "", "investigatable_product_epp_url": "https://falcon.crowdstrike.com/activity-v2/detections/286fb047e7b6443db41dfc3d2679db2f:ind:e7964c714d4d4c8faa8c912595907318:*************-10305-26128?_cid=g03000zndb7s5tynf6flkpqtjlttpgoa", "investigatable_severity": "Medium", "investigatable_status": "New", "trigger_cid": "286fb047e7b6443db41dfc3d2679db2f", "trigger_source_event_url": "https://falcon.crowdstrike.com/activity-v2/detections/286fb047e7b6443db41dfc3d2679db2f:ind:e7964c714d4d4c8faa8c912595907318:*************-10305-26128?_cid=g03000zndb7s5tynf6flkpqtjlttpgoa", "workflow_description": "Note from Fusion team: Workflow auto-generated as a part of your Raptor migration. The migrated workflow uses the new alert trigger and will tag detections previously closed as True Positive or False Positive with a True Positive or False Positive alert tag and will set the alert status to closed.original workflow https://falcon.crowdstrike.com/workflow/fusion/7675b1b2c15347e2976fb7f03c54732e", "workflow_execution_timestamp": "2025-07-31T19:46:40Z", "workflow_execution_timestamp_date": "2025-07-31", "workflow_execution_timestamp_dayofweek": "Thursday", "workflow_execution_timestamp_hour": "19", "workflow_execution_timestamp_minute": "46", "workflow_execution_timestamp_timezone": "UTC", "workflow_name": "CSDetectionsALL-Staging-migrated"}, "meta": {"event_reference_url": "https://falcon.crowdstrike.com/activity-v2/detections/286fb047e7b6443db41dfc3d2679db2f:ind:e7964c714d4d4c8faa8c912595907318:*************-10305-26128?_cid=g03000zndb7s5tynf6flkpqtjlttpgoa", "timestamp": "2025-07-31T19:46:40Z", "trigger_category": "Investigatable/EPP", "trigger_name": "Detection", "workflow_id": "3aedcceec61d73e48e45449903c2d6ae"}}